import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { FileText, User } from "lucide-react";

interface PatientInfo {
  fullName: string;
  email: string;
  phone: string;
  age: string;
  gender: string;
  medicalHistory: string;
  currentConcerns: string;
}

interface PatientInformationProps {
  patientInfo: PatientInfo;
  onPatientInfoChange: (field: keyof PatientInfo, value: string) => void;
}

export function PatientInformation({ patientInfo, onPatientInfoChange }: PatientInformationProps) {
  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
          <User className="w-5 h-5 mr-2" />
          Patient Information
        </h2>

        <div className="space-y-4">
          <div>
            <Label htmlFor="fullName" className="text-sm font-medium text-gray-700">
              Full Name *
            </Label>
            <Input
              id="fullName"
              value={patientInfo.fullName}
              onChange={(e) => onPatientInfoChange('fullName', e.target.value)}
              placeholder="Enter your full name"
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email Address *
              </Label>
              <Input
                id="email"
                type="email"
                value={patientInfo.email}
                onChange={(e) => onPatientInfoChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                Phone Number *
              </Label>
              <Input
                id="phone"
                value={patientInfo.phone}
                onChange={(e) => onPatientInfoChange('phone', e.target.value)}
                placeholder="+91 98765 43210"
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="age" className="text-sm font-medium text-gray-700">
                Age *
              </Label>
              <Input
                id="age"
                type="number"
                value={patientInfo.age}
                onChange={(e) => onPatientInfoChange('age', e.target.value)}
                placeholder="25"
                className="mt-1"
              />
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Gender *
              </Label>
              <Select value={patientInfo.gender} onValueChange={(value) => onPatientInfoChange('gender', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                  <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="medicalHistory" className="text-sm font-medium text-gray-700">
              Medical History
            </Label>
            <Textarea
              id="medicalHistory"
              value={patientInfo.medicalHistory}
              onChange={(e) => onPatientInfoChange('medicalHistory', e.target.value)}
              placeholder="Please share any relevant medical history, current medications, allergies, etc."
              className="mt-1 min-h-[80px]"
            />
          </div>

          <div>
            <Label htmlFor="currentConcerns" className="text-sm font-medium text-gray-700">
              Current Health Concerns *
            </Label>
            <Textarea
              id="currentConcerns"
              value={patientInfo.currentConcerns}
              onChange={(e) => onPatientInfoChange('currentConcerns', e.target.value)}
              placeholder="Describe your current health concerns and what you'd like to discuss with the doctor"
              className="mt-1 min-h-[80px]"
            />
          </div>
        </div>
      </Card>

      {/* Payment Section */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
          <FileText className="w-5 h-5 mr-2" />
          Payment Information
        </h2>

        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium text-gray-700">Consultation Fee</span>
            <span className="font-semibold text-green-600">₹2,000</span>
          </div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Platform Fee</span>
            <span className="text-sm text-gray-600">₹50</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex items-center justify-between">
              <span className="font-semibold text-gray-900">Total Amount</span>
              <span className="font-semibold text-green-600">₹2,050</span>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="p-3 border border-gray-200 rounded-lg bg-white">
            <div className="flex items-center">
              <input type="radio" name="payment" id="upi" className="mr-3" defaultChecked />
              <label htmlFor="upi" className="font-medium text-gray-700">UPI Payment</label>
            </div>
            <p className="text-sm text-gray-500 ml-6">Pay using Google Pay, PhonePe, Paytm</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-white">
            <div className="flex items-center">
              <input type="radio" name="payment" id="card" className="mr-3" />
              <label htmlFor="card" className="font-medium text-gray-700">Credit/Debit Card</label>
            </div>
            <p className="text-sm text-gray-500 ml-6">Visa, Mastercard, RuPay accepted</p>
          </div>

          <div className="p-3 border border-gray-200 rounded-lg bg-white">
            <div className="flex items-center">
              <input type="radio" name="payment" id="netbanking" className="mr-3" />
              <label htmlFor="netbanking" className="font-medium text-gray-700">Net Banking</label>
            </div>
            <p className="text-sm text-gray-500 ml-6">All major banks supported</p>
          </div>
        </div>
      </Card>
    </div>
  );
}