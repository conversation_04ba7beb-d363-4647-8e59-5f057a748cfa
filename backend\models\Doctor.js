const mongoose = require('mongoose');

const doctorSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add doctor name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters'],
  },
  email: {
    type: String,
    required: [true, 'Please add doctor email'],
    unique: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email',
    ],
  },
  phone: {
    type: String,
    required: [true, 'Please add phone number'],
    match: [/^\+?[1-9]\d{1,14}$/, 'Please add a valid phone number'],
  },
  specialization: {
    type: String,
    required: [true, 'Please add specialization'],
    enum: [
      'Panchakarma Specialist',
      'Ayurvedic Nutrition',
      'Pulse Diagnosis Expert',
      'Herbal Medicine',
      'Ayurvedic Dermatology',
      'Ayurvedic Cardiology',
      'Ayurvedic Gynecology',
      'General Ayurveda',
      '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON> Tantra',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>idya',
    ],
  },
  experience: {
    type: Number,
    required: [true, 'Please add years of experience'],
    min: [0, 'Experience cannot be negative'],
  },
  qualifications: [{
    type: String,
    required: false,
  }],
  registrationNumber: {
    type: String,
    required: false, // Made optional to match frontend
    unique: true,
    sparse: true, // Allow multiple null values
  },
  consultationFee: {
    type: Number,
    required: [true, 'Please add consultation fee'],
    min: [0, 'Fee cannot be negative'],
  },
  profileImage: {
    type: String,
    default: '',
  },
  bio: {
    type: String,
    maxlength: [1000, 'Bio cannot be more than 1000 characters'],
  },
  languages: [{
    type: String,
    enum: ['English', 'Hindi', 'Tamil', 'Telugu', 'Malayalam', 'Kannada', 'Bengali', 'Marathi', 'Gujarati', 'Punjabi', 'Urdu', 'Sanskrit'],
  }],
  availability: {
    monday: { start: String, end: String, available: { type: Boolean, default: true } },
    tuesday: { start: String, end: String, available: { type: Boolean, default: true } },
    wednesday: { start: String, end: String, available: { type: Boolean, default: true } },
    thursday: { start: String, end: String, available: { type: Boolean, default: true } },
    friday: { start: String, end: String, available: { type: Boolean, default: true } },
    saturday: { start: String, end: String, available: { type: Boolean, default: true } },
    sunday: { start: String, end: String, available: { type: Boolean, default: false } },
  },
  rating: {
    type: Number,
    default: 0,
    min: [0, 'Rating cannot be less than 0'],
    max: [5, 'Rating cannot be more than 5'],
  },
  totalConsultations: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  joinedDate: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

// Index for search functionality
doctorSchema.index({ name: 'text', specialization: 'text', qualifications: 'text' });

module.exports = mongoose.model('Doctor', doctorSchema);
