const { validationResult } = require('express-validator');
const Doctor = require('../models/Doctor');
const Consultation = require('../models/Consultation');

// Helper function to track changes
const getChangedFields = (original, updated) => {
  const changes = {};
  const excludeFields = ['_id', '__v', 'createdAt', 'updatedAt'];

  for (const key in updated) {
    if (!excludeFields.includes(key) && JSON.stringify(original[key]) !== JSON.stringify(updated[key])) {
      changes[key] = {
        from: original[key],
        to: updated[key],
      };
    }
  }

  return changes;
};

// @desc    Get all doctors
// @route   GET /api/doctors
// @access  Private
const getDoctors = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build query
    let query = {};
    
    // Search functionality
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    // Filter by specialization
    if (req.query.specialization) {
      query.specialization = req.query.specialization;
    }

    // Filter by active status
    if (req.query.isActive !== undefined) {
      query.isActive = req.query.isActive === 'true';
    }

    const doctors = await Doctor.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(startIndex);

    const total = await Doctor.countDocuments(query);

    res.status(200).json({
      success: true,
      count: doctors.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
      data: doctors,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single doctor
// @route   GET /api/doctors/:id
// @access  Private
const getDoctor = async (req, res, next) => {
  try {
    const doctor = await Doctor.findById(req.params.id);

    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    res.status(200).json({
      success: true,
      data: doctor,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new doctor
// @route   POST /api/doctors
// @access  Private (Admin only)
const createDoctor = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    // Check if doctor with email already exists
    const existingDoctor = await Doctor.findOne({ email: req.body.email });
    if (existingDoctor) {
      return res.status(400).json({
        success: false,
        message: 'Doctor with this email already exists',
      });
    }

    // Check if registration number already exists (only if provided)
    if (req.body.registrationNumber) {
      const existingRegNumber = await Doctor.findOne({ registrationNumber: req.body.registrationNumber });
      if (existingRegNumber) {
        return res.status(400).json({
          success: false,
          message: 'Doctor with this registration number already exists',
        });
      }
    }

    const doctor = await Doctor.create(req.body);

    res.status(201).json({
      success: true,
      data: doctor,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update doctor
// @route   PUT /api/doctors/:id
// @access  Private (Admin only)
const updateDoctor = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array(),
      });
    }

    let doctor = await Doctor.findById(req.params.id);

    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    // Store original data for comparison
    const originalData = doctor.toObject();

    // Check for duplicate email (excluding current doctor)
    if (req.body.email && req.body.email !== doctor.email) {
      const existingDoctor = await Doctor.findOne({
        email: req.body.email,
        _id: { $ne: req.params.id }
      });
      if (existingDoctor) {
        return res.status(400).json({
          success: false,
          message: 'Doctor with this email already exists',
        });
      }
    }

    // Check for duplicate registration number (excluding current doctor)
    if (req.body.registrationNumber && req.body.registrationNumber !== doctor.registrationNumber) {
      const existingRegNumber = await Doctor.findOne({
        registrationNumber: req.body.registrationNumber,
        _id: { $ne: req.params.id }
      });
      if (existingRegNumber) {
        return res.status(400).json({
          success: false,
          message: 'Doctor with this registration number already exists',
        });
      }
    }

    // Check for duplicate phone number (excluding current doctor)
    if (req.body.phone && req.body.phone !== doctor.phone) {
      const existingPhone = await Doctor.findOne({
        phone: req.body.phone,
        _id: { $ne: req.params.id }
      });
      if (existingPhone) {
        return res.status(400).json({
          success: false,
          message: 'Doctor with this phone number already exists',
        });
      }
    }

    // Validate consultation fee changes
    if (req.body.consultationFee && req.body.consultationFee !== doctor.consultationFee) {
      // Check if doctor has upcoming consultations
      const upcomingConsultations = await Consultation.countDocuments({
        doctor: req.params.id,
        appointmentDate: { $gte: new Date() },
        status: { $in: ['Scheduled'] },
      });

      if (upcomingConsultations > 0) {
        return res.status(400).json({
          success: false,
          message: `Cannot change consultation fee. Doctor has ${upcomingConsultations} upcoming consultations. Please reschedule or complete them first.`,
        });
      }
    }

    // Validate availability changes
    if (req.body.availability) {
      // Check if changing availability affects upcoming consultations
      const upcomingConsultations = await Consultation.find({
        doctor: req.params.id,
        appointmentDate: { $gte: new Date() },
        status: { $in: ['Scheduled'] },
      }).select('appointmentDate appointmentTime');

      for (const consultation of upcomingConsultations) {
        const dayOfWeek = consultation.appointmentDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const newAvailability = req.body.availability[dayOfWeek];

        if (newAvailability && !newAvailability.available) {
          return res.status(400).json({
            success: false,
            message: `Cannot make doctor unavailable on ${dayOfWeek}. There are scheduled consultations on this day.`,
          });
        }
      }
    }

    // Update doctor with validation
    doctor = await Doctor.findByIdAndUpdate(req.params.id, {
      ...req.body,
      updatedAt: new Date(),
    }, {
      new: true,
      runValidators: true,
    });

    // Log the update for audit trail
    console.log(`Doctor updated: ${doctor.name} (${doctor._id}) by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Doctor updated successfully',
      data: doctor,
      changes: getChangedFields(originalData, doctor.toObject()),
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete doctor (soft delete by default)
// @route   DELETE /api/doctors/:id
// @access  Private (Admin only)
const deleteDoctor = async (req, res, next) => {
  try {
    const { force = false } = req.query; // Force delete parameter
    const doctor = await Doctor.findById(req.params.id);

    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    // Check if doctor has any upcoming consultations
    const upcomingConsultations = await Consultation.find({
      doctor: req.params.id,
      appointmentDate: { $gte: new Date() },
      status: { $in: ['Scheduled', 'In Progress'] },
    }).populate('patient', 'name email');

    if (upcomingConsultations.length > 0 && !force) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete doctor with upcoming consultations',
        upcomingConsultations: upcomingConsultations.length,
        consultations: upcomingConsultations.map(c => ({
          id: c._id,
          patient: c.patient.name,
          date: c.appointmentDate,
          time: c.appointmentTime,
          status: c.status,
        })),
        suggestion: 'Use ?force=true to force delete and cancel all upcoming consultations, or reschedule them first.',
      });
    }

    // Check if doctor has any completed consultations
    const completedConsultations = await Consultation.countDocuments({
      doctor: req.params.id,
      status: 'Completed',
    });

    if (completedConsultations > 0 && !force) {
      // Soft delete - deactivate doctor instead of removing
      const updatedDoctor = await Doctor.findByIdAndUpdate(
        req.params.id,
        {
          isActive: false,
          deactivatedAt: new Date(),
          deactivatedBy: req.user.id,
          deactivationReason: 'Deleted by admin',
        },
        { new: true }
      );

      // Cancel all upcoming consultations
      if (upcomingConsultations.length > 0) {
        await Consultation.updateMany(
          {
            doctor: req.params.id,
            appointmentDate: { $gte: new Date() },
            status: { $in: ['Scheduled', 'In Progress'] },
          },
          {
            status: 'Cancelled',
            cancellationReason: 'Doctor account deactivated',
            cancelledAt: new Date(),
            cancelledBy: req.user.id,
          }
        );
      }

      console.log(`Doctor soft deleted: ${doctor.name} (${doctor._id}) by user ${req.user.id}`);

      return res.status(200).json({
        success: true,
        message: 'Doctor deactivated successfully (soft delete)',
        data: {
          doctor: updatedDoctor,
          completedConsultations,
          cancelledUpcomingConsultations: upcomingConsultations.length,
        },
        note: 'Doctor has been deactivated due to existing consultation history. Use force delete to permanently remove.',
      });
    }

    // Force delete or no consultation history
    if (force || completedConsultations === 0) {
      // Cancel all upcoming consultations if force delete
      if (upcomingConsultations.length > 0) {
        await Consultation.updateMany(
          {
            doctor: req.params.id,
            appointmentDate: { $gte: new Date() },
            status: { $in: ['Scheduled', 'In Progress'] },
          },
          {
            status: 'Cancelled',
            cancellationReason: 'Doctor account deleted',
            cancelledAt: new Date(),
            cancelledBy: req.user.id,
          }
        );
      }

      // Permanently delete doctor
      await Doctor.findByIdAndDelete(req.params.id);

      console.log(`Doctor permanently deleted: ${doctor.name} (${doctor._id}) by user ${req.user.id}`);

      res.status(200).json({
        success: true,
        message: 'Doctor deleted permanently',
        data: {
          deletedDoctor: {
            id: doctor._id,
            name: doctor.name,
            email: doctor.email,
          },
          cancelledConsultations: upcomingConsultations.length,
        },
      });
    }
  } catch (error) {
    next(error);
  }
};

// @desc    Get doctor statistics
// @route   GET /api/doctors/:id/stats
// @access  Private
const getDoctorStats = async (req, res, next) => {
  try {
    const doctor = await Doctor.findById(req.params.id);

    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    // Get consultation statistics
    const totalConsultations = await Consultation.countDocuments({ doctor: req.params.id });
    const completedConsultations = await Consultation.countDocuments({ 
      doctor: req.params.id, 
      status: 'Completed' 
    });
    const upcomingConsultations = await Consultation.countDocuments({
      doctor: req.params.id,
      appointmentDate: { $gte: new Date() },
      status: 'Scheduled',
    });

    // Calculate average rating
    const consultationsWithRating = await Consultation.find({
      doctor: req.params.id,
      'rating.score': { $exists: true },
    }).select('rating.score');

    let averageRating = 0;
    if (consultationsWithRating.length > 0) {
      const totalRating = consultationsWithRating.reduce((sum, consultation) => 
        sum + consultation.rating.score, 0);
      averageRating = totalRating / consultationsWithRating.length;
    }

    // Calculate total revenue
    const revenueData = await Consultation.aggregate([
      { $match: { doctor: doctor._id, 'fee.paymentStatus': 'Paid' } },
      { $group: { _id: null, totalRevenue: { $sum: '$fee.amount' } } },
    ]);

    const totalRevenue = revenueData.length > 0 ? revenueData[0].totalRevenue : 0;

    res.status(200).json({
      success: true,
      data: {
        doctor: {
          id: doctor._id,
          name: doctor.name,
          specialization: doctor.specialization,
        },
        stats: {
          totalConsultations,
          completedConsultations,
          upcomingConsultations,
          averageRating: Math.round(averageRating * 10) / 10,
          totalRevenue,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getDoctors,
  getDoctor,
  createDoctor,
  updateDoctor,
  deleteDoctor,
  getDoctorStats,
};
