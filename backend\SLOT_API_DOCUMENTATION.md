# Slot Management API Documentation

## Overview
The Slot Management API provides comprehensive functionality for managing appointment slots in the Ayura Admin Panel. This includes creating, reading, updating, and deleting slots, as well as advanced features like bulk creation, booking management, and statistics.

## Base URL
```
http://localhost:5000/api/slots
```

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Slot Model
```javascript
{
  _id: "ObjectId",
  doctor: {
    _id: "ObjectId",
    name: "string",
    specialization: "string"
  },
  date: "Date",
  startTime: "string", // HH:MM format
  endTime: "string",   // HH:MM format
  duration: "number",  // in minutes
  status: "available|booked|blocked|cancelled",
  consultationType: "string",
  fee: {
    amount: "number",
    currency: "string"
  },
  patient: {
    _id: "ObjectId",
    name: "string",
    phone: "string"
  },
  consultation: "ObjectId",
  isRecurring: "boolean",
  recurringPattern: {
    frequency: "daily|weekly|monthly",
    interval: "number",
    endDate: "Date"
  },
  notes: "string",
  bookingDetails: {
    bookedAt: "Date",
    bookedBy: "ObjectId",
    cancelledAt: "Date",
    cancelledBy: "ObjectId",
    cancellationReason: "string"
  },
  metadata: {
    createdBy: "ObjectId",
    updatedBy: "ObjectId",
    source: "manual|bulk|recurring|api"
  },
  createdAt: "Date",
  updatedAt: "Date"
}
```

## API Endpoints

### 1. Get All Slots
**GET** `/api/slots`

Retrieve all slots with optional filtering and pagination.

**Query Parameters:**
- `search` (string): Search by doctor name or specialization
- `doctorId` (string): Filter by specific doctor
- `status` (string): Filter by slot status
- `date` (string): Filter by specific date (YYYY-MM-DD)
- `dateFrom` (string): Start date for range filter
- `dateTo` (string): End date for range filter
- `consultationType` (string): Filter by consultation type
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 50)

**Response:**
```javascript
{
  "success": true,
  "data": [/* array of slots */],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 100,
    "pages": 2
  }
}
```

### 2. Get Available Slots
**GET** `/api/slots/available`

Get only available slots for booking.

**Query Parameters:**
- `doctor` (string): Doctor ID
- `date` (string): Specific date

**Response:**
```javascript
{
  "success": true,
  "data": ["09:00", "09:30", "10:00"] // Available time slots
}
```

### 3. Get Slot by ID
**GET** `/api/slots/:id`

Retrieve a specific slot by its ID.

**Response:**
```javascript
{
  "success": true,
  "data": {/* slot object */}
}
```

### 4. Create Slot
**POST** `/api/slots`

Create a new appointment slot.

**Required Permissions:** Admin or Doctor

**Request Body:**
```javascript
{
  "doctorId": "ObjectId",
  "date": "2024-12-20",
  "startTime": "10:00",
  "endTime": "10:30",
  "duration": 30,
  "consultationType": "General Consultation",
  "fee": {
    "amount": 800,
    "currency": "INR"
  },
  "notes": "Optional notes",
  "isRecurring": false,
  "recurringPattern": {
    "frequency": "weekly",
    "interval": 1,
    "endDate": "2024-12-31"
  }
}
```

**Response:**
```javascript
{
  "success": true,
  "data": {/* created slot */},
  "message": "Slot created successfully"
}
```

### 5. Update Slot
**PUT** `/api/slots/:id`

Update an existing slot.

**Required Permissions:** Admin or Doctor

**Request Body:** Same as create slot

**Response:**
```javascript
{
  "success": true,
  "data": {/* updated slot */},
  "message": "Slot updated successfully"
}
```

### 6. Delete Slot
**DELETE** `/api/slots/:id`

Delete a slot (only if not booked).

**Required Permissions:** Admin or Doctor

**Response:**
```javascript
{
  "success": true,
  "message": "Slot deleted successfully"
}
```

### 7. Bulk Create Slots
**POST** `/api/slots/generate`

Create multiple slots at once.

**Required Permissions:** Admin

**Request Body:**
```javascript
{
  "doctorId": "ObjectId",
  "dates": ["2024-12-21", "2024-12-22"],
  "timeSlots": ["09:00", "09:30", "10:00"],
  "duration": 30,
  "consultationType": "General Consultation",
  "fee": {
    "amount": 800,
    "currency": "INR"
  },
  "isRecurring": false,
  "recurringWeeks": 4
}
```

**Response:**
```javascript
{
  "success": true,
  "data": [/* array of created slots */],
  "message": "Successfully created 6 slots"
}
```

### 8. Update Slot Status
**PATCH** `/api/slots/:id/status`

Update the status of a slot.

**Required Permissions:** Admin or Doctor

**Request Body:**
```javascript
{
  "status": "blocked" // available, blocked, cancelled
}
```

**Response:**
```javascript
{
  "success": true,
  "data": {/* updated slot */},
  "message": "Slot status updated to blocked"
}
```

### 9. Book Slot
**POST** `/api/slots/:id/book`

Book a slot for a patient.

**Request Body:**
```javascript
{
  "patientId": "ObjectId",
  "consultationId": "ObjectId" // optional
}
```

**Response:**
```javascript
{
  "success": true,
  "data": {/* booked slot */},
  "message": "Slot booked successfully"
}
```

### 10. Cancel Booking
**POST** `/api/slots/:id/cancel`

Cancel a slot booking.

**Request Body:**
```javascript
{
  "reason": "Patient requested cancellation"
}
```

**Response:**
```javascript
{
  "success": true,
  "data": {/* cancelled slot */},
  "message": "Booking cancelled successfully"
}
```

### 11. Get Doctor Slots
**GET** `/api/slots/doctor/:doctorId`

Get all slots for a specific doctor within a date range.

**Query Parameters:**
- `startDate` (string): Start date (YYYY-MM-DD)
- `endDate` (string): End date (YYYY-MM-DD)

**Response:**
```javascript
{
  "success": true,
  "data": [/* doctor's slots */]
}
```

### 12. Get Slot Statistics
**GET** `/api/slots/stats`

Get comprehensive slot statistics.

**Query Parameters:**
- `doctorId` (string): Filter by doctor
- `startDate` (string): Start date for stats
- `endDate` (string): End date for stats

**Response:**
```javascript
{
  "success": true,
  "data": {
    "totalSlots": 100,
    "availableSlots": 60,
    "bookedSlots": 30,
    "blockedSlots": 5,
    "cancelledSlots": 5,
    "totalRevenue": 24000,
    "utilizationRate": "30.00"
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```javascript
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information" // in development mode
}
```

## Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors, business logic violations)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## Business Rules

1. **Slot Overlap Prevention**: Slots cannot overlap for the same doctor
2. **Past Slot Restrictions**: Cannot create or modify slots in the past
3. **Booking Restrictions**: Only available slots can be booked
4. **Deletion Restrictions**: Booked slots cannot be deleted
5. **Status Transitions**: Booked slots can only be cancelled, not blocked
6. **Time Validation**: End time must be after start time
7. **Duration Validation**: Duration must match start and end times

## Testing

Run the test suite to verify all endpoints:

```bash
cd backend
node test-slot-apis.js
```

This will test all CRUD operations, bulk creation, booking, and statistics endpoints.
