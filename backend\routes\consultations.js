const express = require('express');
const { body } = require('express-validator');
const {
  getConsultations,
  getConsultation,
  createConsultation,
  updateConsultation,
  deleteConsultation,
  updateConsultationStatus,
  getConsultationsByDoctor,
  getConsultationsByPatient,
} = require('../controllers/consultationController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules for creating consultation
const consultationValidation = [
  body('patient').isMongoId().withMessage('Valid patient ID is required'),
  body('doctor').isMongoId().withMessage('Valid doctor ID is required'),
  body('appointmentDate').isISO8601().withMessage('Valid appointment date is required'),
  body('appointmentTime').notEmpty().withMessage('Appointment time is required'),
  body('type').isIn(['Initial Consultation', 'Follow-up', '<PERSON><PERSON><PERSON><PERSON>', 'General Checkup', 'Emergency'])
    .withMessage('Invalid consultation type'),
  body('mode').isIn(['In-person', 'Video Call', 'Phone Call']).withMessage('Invalid consultation mode'),
  body('fee.amount').isFloat({ min: 0 }).withMessage('Fee amount must be a positive number'),
];

// Validation rules for updating consultation (optional fields)
const consultationUpdateValidation = [
  body('patient').optional().isMongoId().withMessage('Valid patient ID is required'),
  body('doctor').optional().isMongoId().withMessage('Valid doctor ID is required'),
  body('appointmentDate').optional().isISO8601().withMessage('Valid appointment date is required'),
  body('appointmentTime').optional().notEmpty().withMessage('Appointment time cannot be empty'),
  body('type').optional().isIn(['Initial Consultation', 'Follow-up', 'Panchakarma', 'General Checkup', 'Emergency'])
    .withMessage('Invalid consultation type'),
  body('mode').optional().isIn(['In-person', 'Video Call', 'Phone Call']).withMessage('Invalid consultation mode'),
  body('status').optional().isIn(['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'])
    .withMessage('Invalid status'),
  body('fee.amount').optional().isFloat({ min: 0 }).withMessage('Fee amount must be a positive number'),
  body('chiefComplaint').optional().isLength({ max: 500 }).withMessage('Chief complaint must not exceed 500 characters'),
  body('notes').optional().isLength({ max: 2000 }).withMessage('Notes must not exceed 2000 characters'),
  body('diagnosis.primary').optional().isLength({ max: 200 }).withMessage('Primary diagnosis must not exceed 200 characters'),
  body('vitals.bloodPressure').optional().matches(/^\d{2,3}\/\d{2,3}$/).withMessage('Blood pressure must be in format XXX/XX'),
  body('vitals.heartRate').optional().isInt({ min: 30, max: 200 }).withMessage('Heart rate must be between 30 and 200'),
  body('vitals.temperature').optional().isFloat({ min: 90, max: 110 }).withMessage('Temperature must be between 90 and 110 Fahrenheit'),
];

const statusValidation = [
  body('status').isIn(['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'])
    .withMessage('Invalid status'),
];

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(getConsultations)
  .post(consultationValidation, createConsultation);

router.route('/:id')
  .get(getConsultation)
  .put(consultationUpdateValidation, updateConsultation)
  .delete(authorize('admin'), deleteConsultation);

router.put('/:id/status', statusValidation, updateConsultationStatus);
router.get('/doctor/:doctorId', getConsultationsByDoctor);
router.get('/patient/:patientId', getConsultationsByPatient);

module.exports = router;
