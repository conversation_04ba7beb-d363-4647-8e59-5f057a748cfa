import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Calendar } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Switch } from '../ui/switch';
import { Checkbox } from '../ui/checkbox';
import { CalendarIcon, Clock, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { Slot, SlotFormData, FormErrors, Doctor } from '../../types/models';
import { doctorsAPI } from '../../utils/api';

interface SlotFormProps {
  slot?: Slot;
  onSubmit: (data: SlotFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const consultationTypes = [
  'General Consultation',
  'Panchakarma',
  'Pulse Diagnosis',
  'Herbal Medicine',
  'Ayurvedic Nutrition',
  'Follow-up',
  'Emergency'
];

const timeSlots = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
];

const durations = [
  { value: 15, label: '15 minutes' },
  { value: 30, label: '30 minutes' },
  { value: 45, label: '45 minutes' },
  { value: 60, label: '60 minutes' },
  { value: 90, label: '90 minutes' }
];

export function SlotForm({ slot, onSubmit, onCancel, isLoading = false }: SlotFormProps) {
  const [formData, setFormData] = useState<SlotFormData>({
    doctorId: slot?.doctor._id || '',
    date: slot?.date || '',
    startTime: slot?.startTime || '',
    endTime: slot?.endTime || '',
    duration: slot?.duration || 30,
    consultationType: slot?.consultationType || '',
    fee: {
      amount: slot?.fee?.amount || 0,
      currency: slot?.fee?.currency || 'INR'
    },
    isRecurring: slot?.isRecurring || false,
    recurringPattern: slot?.recurringPattern || {
      frequency: 'weekly',
      interval: 1,
      endDate: ''
    },
    notes: slot?.notes || ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    slot?.date ? new Date(slot.date) : new Date()
  );

  useEffect(() => {
    fetchDoctors();
  }, []);

  useEffect(() => {
    if (selectedDate) {
      setFormData(prev => ({
        ...prev,
        date: format(selectedDate, 'yyyy-MM-dd')
      }));
    }
  }, [selectedDate]);

  useEffect(() => {
    // Calculate end time when start time or duration changes
    if (formData.startTime && formData.duration) {
      const [hours, minutes] = formData.startTime.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + formData.duration;
      const endHours = Math.floor(endMinutes / 60);
      const endMins = endMinutes % 60;
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
      
      setFormData(prev => ({ ...prev, endTime }));
    }
  }, [formData.startTime, formData.duration]);

  const fetchDoctors = async () => {
    try {
      setLoadingDoctors(true);
      const response = await doctorsAPI.getAll();
      if (response.success) {
        setDoctors(response.data);
      }
    } catch (error) {
      console.error('Error fetching doctors:', error);
    } finally {
      setLoadingDoctors(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.doctorId) newErrors.doctorId = 'Doctor is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.startTime) newErrors.startTime = 'Start time is required';
    if (!formData.consultationType) newErrors.consultationType = 'Consultation type is required';
    if (formData.duration <= 0) newErrors.duration = 'Duration must be positive';
    if (formData.fee.amount <= 0) newErrors.fee = 'Fee must be positive';

    // Date validation - slot should be in the future for new slots
    if (!slot && formData.date) {
      const slotDate = new Date(`${formData.date}T${formData.startTime}`);
      const now = new Date();
      if (slotDate <= now) {
        newErrors.date = 'Slot must be in the future';
      }
    }

    // Recurring pattern validation
    if (formData.isRecurring) {
      if (!formData.recurringPattern?.endDate) {
        newErrors.recurringEndDate = 'End date is required for recurring slots';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('SlotForm: Submitting form with data:', formData);

    if (validateForm()) {
      console.log('SlotForm: Validation passed, calling onSubmit');
      await onSubmit(formData);
    } else {
      console.log('SlotForm: Validation failed, errors:', errors);
    }
  };

  const handleInputChange = (field: keyof SlotFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleFeeChange = (field: 'amount' | 'currency', value: any) => {
    setFormData(prev => ({
      ...prev,
      fee: { ...prev.fee, [field]: value }
    }));
    if (errors.fee) {
      setErrors(prev => ({ ...prev, fee: '' }));
    }
  };

  const handleRecurringChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      recurringPattern: {
        ...prev.recurringPattern!,
        [field]: value
      }
    }));
  };

  const selectedDoctor = doctors.find(d => d._id === formData.doctorId);

  // Auto-fill fee when doctor is selected
  useEffect(() => {
    if (selectedDoctor && !slot) {
      setFormData(prev => ({
        ...prev,
        fee: { ...prev.fee, amount: selectedDoctor.consultationFee }
      }));
    }
  }, [selectedDoctor, slot]);

  if (loadingDoctors) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>Loading doctors...</p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Slot Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="doctorId">Doctor *</Label>
              <Select
                value={formData.doctorId}
                onValueChange={(value) => handleInputChange('doctorId', value)}
              >
                <SelectTrigger className={errors.doctorId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select doctor" />
                </SelectTrigger>
                <SelectContent>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor._id} value={doctor._id}>
                      {doctor.name} - {doctor.specialization}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.doctorId && <p className="text-red-500 text-sm mt-1">{errors.doctorId}</p>}
            </div>

            <div>
              <Label htmlFor="consultationType">Consultation Type *</Label>
              <Select
                value={formData.consultationType}
                onValueChange={(value) => handleInputChange('consultationType', value)}
              >
                <SelectTrigger className={errors.consultationType ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {consultationTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.consultationType && <p className="text-red-500 text-sm mt-1">{errors.consultationType}</p>}
            </div>

            <div>
              <Label htmlFor="date">Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className={`w-full justify-start ${errors.date ? 'border-red-500' : ''}`}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.date && <p className="text-red-500 text-sm mt-1">{errors.date}</p>}
            </div>

            <div>
              <Label htmlFor="startTime">Start Time *</Label>
              <Select
                value={formData.startTime}
                onValueChange={(value) => handleInputChange('startTime', value)}
              >
                <SelectTrigger className={errors.startTime ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>
                  {timeSlots.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.startTime && <p className="text-red-500 text-sm mt-1">{errors.startTime}</p>}
            </div>

            <div>
              <Label htmlFor="duration">Duration *</Label>
              <Select
                value={formData.duration.toString()}
                onValueChange={(value) => handleInputChange('duration', parseInt(value))}
              >
                <SelectTrigger className={errors.duration ? 'border-red-500' : ''}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {durations.map((duration) => (
                    <SelectItem key={duration.value} value={duration.value.toString()}>
                      {duration.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.duration && <p className="text-red-500 text-sm mt-1">{errors.duration}</p>}
            </div>

            <div>
              <Label htmlFor="fee">Fee (₹) *</Label>
              <Input
                id="fee"
                type="number"
                min="0"
                value={formData.fee.amount}
                onChange={(e) => handleFeeChange('amount', parseInt(e.target.value) || 0)}
                className={errors.fee ? 'border-red-500' : ''}
              />
              {errors.fee && <p className="text-red-500 text-sm mt-1">{errors.fee}</p>}
            </div>
          </div>

          {formData.endTime && (
            <div className="text-sm text-muted-foreground">
              <Clock className="inline h-4 w-4 mr-1" />
              End Time: {formData.endTime}
            </div>
          )}

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes for this slot..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Recurring Pattern */}
      <Card>
        <CardHeader>
          <CardTitle>Recurring Pattern</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isRecurring"
              checked={formData.isRecurring}
              onCheckedChange={(checked) => handleInputChange('isRecurring', checked)}
            />
            <Label htmlFor="isRecurring">Make this a recurring slot</Label>
          </div>

          {formData.isRecurring && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
              <div>
                <Label htmlFor="frequency">Frequency</Label>
                <Select
                  value={formData.recurringPattern?.frequency}
                  onValueChange={(value) => handleRecurringChange('frequency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="interval">Interval</Label>
                <Select
                  value={formData.recurringPattern?.interval.toString()}
                  onValueChange={(value) => handleRecurringChange('interval', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Every 1</SelectItem>
                    <SelectItem value="2">Every 2</SelectItem>
                    <SelectItem value="3">Every 3</SelectItem>
                    <SelectItem value="4">Every 4</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.recurringPattern?.endDate}
                  onChange={(e) => handleRecurringChange('endDate', e.target.value)}
                  className={errors.recurringEndDate ? 'border-red-500' : ''}
                />
                {errors.recurringEndDate && <p className="text-red-500 text-sm mt-1">{errors.recurringEndDate}</p>}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Doctor Info */}
      {selectedDoctor && (
        <Card>
          <CardHeader>
            <CardTitle>Doctor Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-1">
              <p><span className="font-medium">Name:</span> {selectedDoctor.name}</p>
              <p><span className="font-medium">Specialization:</span> {selectedDoctor.specialization}</p>
              <p><span className="font-medium">Experience:</span> {selectedDoctor.experience} years</p>
              <p><span className="font-medium">Default Fee:</span> ₹{selectedDoctor.consultationFee}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : slot ? 'Update Slot' : 'Create Slot'}
        </Button>
      </div>
    </form>
  );
}
