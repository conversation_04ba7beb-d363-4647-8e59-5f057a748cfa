const express = require('express');
const { body } = require('express-validator');
const {
  getDoctors,
  getDoctor,
  createDoctor,
  updateDoctor,
  deleteDoctor,
  getDoctorStats,
} = require('../controllers/doctorController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules for creating doctor
const doctorValidation = [
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('phone').isMobilePhone().withMessage('Please provide a valid phone number'),
  body('specialization').notEmpty().withMessage('Specialization is required'),
  body('experience').isInt({ min: 0 }).withMessage('Experience must be a positive number'),
  body('qualifications').optional().isArray().withMessage('Qualifications must be an array'),
  body('registrationNumber').optional().notEmpty().withMessage('Registration number cannot be empty'),
  body('consultationFee').isFloat({ min: 0 }).withMessage('Consultation fee must be a positive number'),
  body('bio').optional().isLength({ max: 1000 }).withMessage('Bio must not exceed 1000 characters'),
  body('languages').optional().isArray().withMessage('Languages must be an array'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
];

// Validation rules for updating doctor (optional fields)
const doctorUpdateValidation = [
  body('name').optional().trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('phone').optional().isMobilePhone().withMessage('Please provide a valid phone number'),
  body('specialization').optional().notEmpty().withMessage('Specialization cannot be empty'),
  body('experience').optional().isInt({ min: 0 }).withMessage('Experience must be a positive number'),
  body('qualifications').optional().isArray().withMessage('Qualifications must be an array'),
  body('registrationNumber').optional().notEmpty().withMessage('Registration number cannot be empty'),
  body('consultationFee').optional().isFloat({ min: 0 }).withMessage('Consultation fee must be a positive number'),
  body('bio').optional().isLength({ max: 1000 }).withMessage('Bio must not exceed 1000 characters'),
  body('languages').optional().isArray().withMessage('Languages must be an array'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
];

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(getDoctors)
  .post(authorize('admin'), doctorValidation, createDoctor);

router.route('/:id')
  .get(getDoctor)
  .put(authorize('admin'), doctorUpdateValidation, updateDoctor)
  .delete(authorize('admin'), deleteDoctor);

router.get('/:id/stats', getDoctorStats);

module.exports = router;
