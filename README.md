
# Ayura Admin Panel

A comprehensive admin panel for Ayurvedic consultation management built with React, TypeScript, and Node.js.

## 🌟 Features

### Frontend (React + TypeScript + Vite)
- **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Real-time Updates**: Live data updates and notifications
- **Type Safety**: Full TypeScript implementation
- **Component Library**: Reusable UI components

### Backend (Node.js + Express + MongoDB)
- **RESTful API**: Complete CRUD operations
- **Authentication**: JWT-based secure authentication
- **Database**: MongoDB with Mongoose ODM
- **Validation**: Input validation and sanitization
- **Error Handling**: Comprehensive error management
- **API Documentation**: Detailed endpoint documentation

### Core Modules
- 👨‍⚕️ **Doctor Management**: Complete doctor profiles and scheduling
- 👥 **Patient Management**: Patient records and history
- 📅 **Slot Management**: Appointment slot creation and booking
- 💬 **Consultation Management**: Consultation tracking and notes
- 📊 **Analytics Dashboard**: Insights and reporting
- ⚙️ **Settings**: System configuration and preferences

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/iamoxygen/ayura-admin-panel.git
   cd ayura-admin-panel
   ```

2. **Install dependencies:**
   ```bash
   # Install frontend dependencies
   npm install

   # Install backend dependencies
   cd backend
   npm install
   cd ..
   ```

3. **Configure environment:**
   ```bash
   # Copy backend environment file
   cp backend/.env.example backend/.env

   # Edit the .env file with your MongoDB connection and other settings
   ```

4. **Start development servers:**
   ```bash
   # Start both frontend and backend
   npm start
   ```

   Or start them separately:
   ```bash
   # Terminal 1 - Frontend (http://localhost:3000)
   npm run dev:frontend

   # Terminal 2 - Backend (http://localhost:5000)
   npm run dev:backend
   ```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm start` | Start both frontend and backend |
| `npm run dev:frontend` | Start frontend only |
| `npm run dev:backend` | Start backend only |
| `npm run dev:both` | Start both with concurrently |
| `npm run build` | Build frontend for production |
| `npm run start:dev` | Smart startup with dependency checking |

## 🌐 Access URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health

## 🔐 Authentication

Default admin credentials:
- **Email**: <EMAIL>
- **Password**: admin123

## 📚 API Documentation

The API includes comprehensive endpoints for:

- **Authentication**: `/api/auth/*`
- **Doctors**: `/api/doctors/*`
- **Patients**: `/api/patients/*`
- **Slots**: `/api/slots/*`
- **Consultations**: `/api/consultations/*`
- **Analytics**: `/api/analytics/*`

For detailed API documentation, see [backend/SLOT_API_DOCUMENTATION.md](backend/SLOT_API_DOCUMENTATION.md)

## 🧪 Testing

```bash
# Test backend APIs
cd backend
npm run test

# Test slot management APIs specifically
node test-slot-apis.js
```

## 🛠️ Development

### Code Style
- Frontend: TypeScript + React best practices
- Backend: Node.js + Express conventions
- Database: MongoDB with Mongoose schemas

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the [DEVELOPMENT.md](DEVELOPMENT.md) guide
- Review the API documentation

---

**Original Design**: https://www.figma.com/design/5Ew5SD5kgoX0hdrqfgBAUS/Ayura-Admin-Panel

Built with ❤️ for Ayurvedic healthcare management
  