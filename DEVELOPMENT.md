# Development Guide - <PERSON><PERSON><PERSON>min Panel

## 🚀 Quick Start

### Option 1: Run Both Frontend and Backend Together (Recommended)

```bash
# Install dependencies and start both servers
npm run start:dev
```

OR

```bash
# Start both servers (if dependencies are already installed)
npm run dev:full
```

This will start:
- **Frontend** on `http://localhost:3000` (Vite dev server)
- **Backend** on `http://localhost:5000` (Express API server)

### Option 2: Run Servers Separately

```bash
# Terminal 1 - Frontend only
npm run dev:frontend

# Terminal 2 - Backend only  
npm run dev:backend
```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start frontend only (Vite) |
| `npm run dev:frontend` | Start frontend only (alias) |
| `npm run dev:backend` | Start backend only (Express) |
| `npm run dev:full` | Start both frontend and backend with concurrently |
| `npm run start` | Alias for `dev:full` |
| `npm run start:dev` | Smart startup script with dependency checking |
| `npm run build` | Build frontend for production |

## 🎨 Concurrently Output

When using `npm run dev:full`, you'll see colored output:
- **🔵 FRONTEND** - Cyan colored logs from Vite
- **🟡 BACKEND** - Yellow colored logs from Express

## 🔧 Development Setup

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (running locally or connection string in `.env`)

### First Time Setup

1. **Clone and install frontend dependencies:**
   ```bash
   npm install
   ```

2. **Install backend dependencies:**
   ```bash
   cd backend
   npm install
   cd ..
   ```

3. **Configure environment:**
   ```bash
   # Copy and edit backend environment file
   cp backend/.env.example backend/.env
   ```

4. **Start development:**
   ```bash
   npm run start:dev
   ```

## 🌐 Server URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health

## 🛠️ Development Features

### Hot Reload
- **Frontend**: Automatic reload on file changes (Vite HMR)
- **Backend**: Manual restart required (or use nodemon)

### API Testing
- Use the "Test API" button in the frontend
- Or test endpoints directly: `GET http://localhost:5000/api/health`

### Database
- MongoDB connection configured in `backend/.env`
- Default: `mongodb://localhost:27017/ayura_admin`

## 🐛 Troubleshooting

### Port Conflicts
If ports 3000 or 5000 are in use:
- Frontend will automatically try port 3001, 3002, etc.
- Backend port can be changed in `backend/.env`

### Backend Not Starting
```bash
# Check if MongoDB is running
mongosh

# Check backend dependencies
cd backend && npm install

# Check environment file
cat backend/.env
```

### Frontend Build Issues
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 📁 Project Structure

```
ayura-admin-panel/
├── src/                    # Frontend source code
├── backend/               # Backend API server
│   ├── controllers/       # API controllers
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   └── server.js         # Express server
├── package.json          # Frontend dependencies & scripts
├── start-dev.js          # Smart development startup script
└── DEVELOPMENT.md        # This file
```

## 🚀 Production Deployment

```bash
# Build frontend
npm run build

# Start backend in production
cd backend
NODE_ENV=production node server.js
```

## 💡 Tips

1. **Use `npm run start:dev`** for the best development experience
2. **Check browser console** for frontend errors
3. **Check terminal output** for backend errors
4. **Use the Test API button** to verify backend connectivity
5. **MongoDB must be running** before starting the backend

## 🔗 Useful Links

- [Vite Documentation](https://vitejs.dev/)
- [Express.js Documentation](https://expressjs.com/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [React Documentation](https://react.dev/)
