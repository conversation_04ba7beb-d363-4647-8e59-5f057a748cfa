import { useState } from "react";
import { Save, Settings, Users, Bell, Shield, Database, Palette, Code, CreditCard, Mail, Globe, Clock, Upload } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Switch } from "./ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Textarea } from "./ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Separator } from "./ui/separator";
import { Badge } from "./ui/badge";

interface SettingsConfig {
  platform: {
    platformName: string;
    supportEmail: string;
    supportPhone: string;
    timezone: string;
    defaultLanguage: string;
    maintenanceMode: boolean;
    registrationEnabled: boolean;
    maxDailyConsultations: number;
    consultationBuffer: number;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    appointmentReminders: boolean;
    paymentAlerts: boolean;
    doctorAlerts: boolean;
    systemAlerts: boolean;
    reminderTime: number;
  };
  security: {
    passwordMinLength: number;
    requireSpecialChars: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    twoFactorAuth: boolean;
    dataRetentionDays: number;
    encryptSensitiveData: boolean;
  };
  payments: {
    currency: string;
    paymentMethods: string[];
    autoRefund: boolean;
    refundPeriod: number;
    taxRate: number;
    processingFee: number;
  };
  branding: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl: string;
    faviconUrl: string;
    customCSS: string;
    footerText: string;
  };
  integrations: {
    googleCalendar: boolean;
    zoomIntegration: boolean;
    whatsappApi: boolean;
    emailProvider: string;
    smsProvider: string;
    paymentGateway: string;
  };
}

const defaultSettings: SettingsConfig = {
  platform: {
    platformName: "Ayura - Ayurvedic Consultations",
    supportEmail: "<EMAIL>",
    supportPhone: "+91 98765 43210",
    timezone: "Asia/Kolkata",
    defaultLanguage: "en",
    maintenanceMode: false,
    registrationEnabled: true,
    maxDailyConsultations: 50,
    consultationBuffer: 15,
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: false,
    appointmentReminders: true,
    paymentAlerts: true,
    doctorAlerts: true,
    systemAlerts: true,
    reminderTime: 24,
  },
  security: {
    passwordMinLength: 8,
    requireSpecialChars: true,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    twoFactorAuth: false,
    dataRetentionDays: 2555, // 7 years
    encryptSensitiveData: true,
  },
  payments: {
    currency: "INR",
    paymentMethods: ["razorpay", "stripe", "paytm"],
    autoRefund: false,
    refundPeriod: 7,
    taxRate: 18,
    processingFee: 2.5,
  },
  branding: {
    primaryColor: "#93a580",
    secondaryColor: "#7a9167",
    logoUrl: "",
    faviconUrl: "",
    customCSS: "",
    footerText: "© 2024 Ayura. All rights reserved.",
  },
  integrations: {
    googleCalendar: false,
    zoomIntegration: true,
    whatsappApi: false,
    emailProvider: "sendgrid",
    smsProvider: "twilio",
    paymentGateway: "razorpay",
  },
};

export function SettingsManager() {
  const [settings, setSettings] = useState<SettingsConfig>(defaultSettings);
  const [hasChanges, setHasChanges] = useState(false);

  const updateSetting = (category: keyof SettingsConfig, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    // In a real application, this would save to backend
    console.log("Saving settings:", settings);
    setHasChanges(false);
  };

  const handleReset = () => {
    setSettings(defaultSettings);
    setHasChanges(true);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1>Settings</h1>
          <p className="text-muted-foreground">Configure system settings and preferences</p>
        </div>
        <div className="flex items-center space-x-2">
          {hasChanges && (
            <Badge variant="secondary" className="bg-orange-100 text-orange-700">
              Unsaved Changes
            </Badge>
          )}
          <Button variant="outline" onClick={handleReset}>
            Reset to Defaults
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges}
            className="bg-primary hover:bg-primary/90"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="platform" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="platform">Platform</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="branding">Branding</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        {/* Platform Settings */}
        <TabsContent value="platform" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Platform Configuration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="platform-name">Platform Name</Label>
                  <Input
                    id="platform-name"
                    value={settings.platform.platformName}
                    onChange={(e) => updateSetting('platform', 'platformName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select 
                    value={settings.platform.timezone} 
                    onValueChange={(value) => updateSetting('platform', 'timezone', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Asia/Kolkata">Asia/Kolkata (IST)</SelectItem>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">America/New_York (EST)</SelectItem>
                      <SelectItem value="Europe/London">Europe/London (GMT)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="support-email">Support Email</Label>
                  <Input
                    id="support-email"
                    type="email"
                    value={settings.platform.supportEmail}
                    onChange={(e) => updateSetting('platform', 'supportEmail', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="support-phone">Support Phone</Label>
                  <Input
                    id="support-phone"
                    value={settings.platform.supportPhone}
                    onChange={(e) => updateSetting('platform', 'supportPhone', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-consultations">Max Daily Consultations</Label>
                  <Input
                    id="max-consultations"
                    type="number"
                    value={settings.platform.maxDailyConsultations}
                    onChange={(e) => updateSetting('platform', 'maxDailyConsultations', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="consultation-buffer">Consultation Buffer (minutes)</Label>
                  <Input
                    id="consultation-buffer"
                    type="number"
                    value={settings.platform.consultationBuffer}
                    onChange={(e) => updateSetting('platform', 'consultationBuffer', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4>Platform Status</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                    <p className="text-sm text-muted-foreground">Temporarily disable the platform for maintenance</p>
                  </div>
                  <Switch
                    id="maintenance-mode"
                    checked={settings.platform.maintenanceMode}
                    onCheckedChange={(checked) => updateSetting('platform', 'maintenanceMode', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="registration-enabled">New User Registration</Label>
                    <p className="text-sm text-muted-foreground">Allow new users to register on the platform</p>
                  </div>
                  <Switch
                    id="registration-enabled"
                    checked={settings.platform.registrationEnabled}
                    onCheckedChange={(checked) => updateSetting('platform', 'registrationEnabled', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="w-5 h-5" />
                <span>Notification Preferences</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4>Notification Channels</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email-notifications">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">Send notifications via email</p>
                    </div>
                    <Switch
                      id="email-notifications"
                      checked={settings.notifications.emailNotifications}
                      onCheckedChange={(checked) => updateSetting('notifications', 'emailNotifications', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="sms-notifications">SMS Notifications</Label>
                      <p className="text-sm text-muted-foreground">Send notifications via SMS</p>
                    </div>
                    <Switch
                      id="sms-notifications"
                      checked={settings.notifications.smsNotifications}
                      onCheckedChange={(checked) => updateSetting('notifications', 'smsNotifications', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="push-notifications">Push Notifications</Label>
                      <p className="text-sm text-muted-foreground">Send browser push notifications</p>
                    </div>
                    <Switch
                      id="push-notifications"
                      checked={settings.notifications.pushNotifications}
                      onCheckedChange={(checked) => updateSetting('notifications', 'pushNotifications', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4>Notification Types</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="appointment-reminders">Appointment Reminders</Label>
                      <p className="text-sm text-muted-foreground">Remind patients about upcoming appointments</p>
                    </div>
                    <Switch
                      id="appointment-reminders"
                      checked={settings.notifications.appointmentReminders}
                      onCheckedChange={(checked) => updateSetting('notifications', 'appointmentReminders', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="payment-alerts">Payment Alerts</Label>
                      <p className="text-sm text-muted-foreground">Notify about payment confirmations and failures</p>
                    </div>
                    <Switch
                      id="payment-alerts"
                      checked={settings.notifications.paymentAlerts}
                      onCheckedChange={(checked) => updateSetting('notifications', 'paymentAlerts', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="doctor-alerts">Doctor Alerts</Label>
                      <p className="text-sm text-muted-foreground">Notify doctors about new appointments</p>
                    </div>
                    <Switch
                      id="doctor-alerts"
                      checked={settings.notifications.doctorAlerts}
                      onCheckedChange={(checked) => updateSetting('notifications', 'doctorAlerts', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="system-alerts">System Alerts</Label>
                      <p className="text-sm text-muted-foreground">Critical system notifications for admins</p>
                    </div>
                    <Switch
                      id="system-alerts"
                      checked={settings.notifications.systemAlerts}
                      onCheckedChange={(checked) => updateSetting('notifications', 'systemAlerts', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="reminder-time">Reminder Time (hours before appointment)</Label>
                <Select 
                  value={settings.notifications.reminderTime.toString()} 
                  onValueChange={(value) => updateSetting('notifications', 'reminderTime', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 hour</SelectItem>
                    <SelectItem value="2">2 hours</SelectItem>
                    <SelectItem value="6">6 hours</SelectItem>
                    <SelectItem value="12">12 hours</SelectItem>
                    <SelectItem value="24">24 hours</SelectItem>
                    <SelectItem value="48">48 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="w-5 h-5" />
                <span>Security Configuration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password-length">Minimum Password Length</Label>
                  <Input
                    id="password-length"
                    type="number"
                    value={settings.security.passwordMinLength}
                    onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value))}
                    min="6"
                    max="20"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-login-attempts">Max Login Attempts</Label>
                  <Input
                    id="max-login-attempts"
                    type="number"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="data-retention">Data Retention (days)</Label>
                  <Input
                    id="data-retention"
                    type="number"
                    value={settings.security.dataRetentionDays}
                    onChange={(e) => updateSetting('security', 'dataRetentionDays', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4>Security Features</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="require-special-chars">Require Special Characters</Label>
                      <p className="text-sm text-muted-foreground">Passwords must contain special characters</p>
                    </div>
                    <Switch
                      id="require-special-chars"
                      checked={settings.security.requireSpecialChars}
                      onCheckedChange={(checked) => updateSetting('security', 'requireSpecialChars', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="two-factor-auth">Two-Factor Authentication</Label>
                      <p className="text-sm text-muted-foreground">Require 2FA for admin accounts</p>
                    </div>
                    <Switch
                      id="two-factor-auth"
                      checked={settings.security.twoFactorAuth}
                      onCheckedChange={(checked) => updateSetting('security', 'twoFactorAuth', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="encrypt-sensitive-data">Encrypt Sensitive Data</Label>
                      <p className="text-sm text-muted-foreground">Encrypt patient data and medical records</p>
                    </div>
                    <Switch
                      id="encrypt-sensitive-data"
                      checked={settings.security.encryptSensitiveData}
                      onCheckedChange={(checked) => updateSetting('security', 'encryptSensitiveData', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payments Settings */}
        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="w-5 h-5" />
                <span>Payment Configuration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Default Currency</Label>
                  <Select 
                    value={settings.payments.currency} 
                    onValueChange={(value) => updateSetting('payments', 'currency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INR">Indian Rupee (INR)</SelectItem>
                      <SelectItem value="USD">US Dollar (USD)</SelectItem>
                      <SelectItem value="EUR">Euro (EUR)</SelectItem>
                      <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="refund-period">Refund Period (days)</Label>
                  <Input
                    id="refund-period"
                    type="number"
                    value={settings.payments.refundPeriod}
                    onChange={(e) => updateSetting('payments', 'refundPeriod', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                  <Input
                    id="tax-rate"
                    type="number"
                    step="0.1"
                    value={settings.payments.taxRate}
                    onChange={(e) => updateSetting('payments', 'taxRate', parseFloat(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="processing-fee">Processing Fee (%)</Label>
                  <Input
                    id="processing-fee"
                    type="number"
                    step="0.1"
                    value={settings.payments.processingFee}
                    onChange={(e) => updateSetting('payments', 'processingFee', parseFloat(e.target.value))}
                  />
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-refund">Automatic Refunds</Label>
                  <p className="text-sm text-muted-foreground">Automatically process refunds for cancelled appointments</p>
                </div>
                <Switch
                  id="auto-refund"
                  checked={settings.payments.autoRefund}
                  onCheckedChange={(checked) => updateSetting('payments', 'autoRefund', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Branding Settings */}
        <TabsContent value="branding" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="w-5 h-5" />
                <span>Branding & Customization</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="primary-color"
                      value={settings.branding.primaryColor}
                      onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
                    />
                    <div 
                      className="w-10 h-10 rounded border border-gray-300"
                      style={{ backgroundColor: settings.branding.primaryColor }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondary-color">Secondary Color</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="secondary-color"
                      value={settings.branding.secondaryColor}
                      onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
                    />
                    <div 
                      className="w-10 h-10 rounded border border-gray-300"
                      style={{ backgroundColor: settings.branding.secondaryColor }}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="logo-url">Logo URL</Label>
                  <Input
                    id="logo-url"
                    value={settings.branding.logoUrl}
                    onChange={(e) => updateSetting('branding', 'logoUrl', e.target.value)}
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="favicon-url">Favicon URL</Label>
                  <Input
                    id="favicon-url"
                    value={settings.branding.faviconUrl}
                    onChange={(e) => updateSetting('branding', 'faviconUrl', e.target.value)}
                    placeholder="https://example.com/favicon.ico"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="footer-text">Footer Text</Label>
                <Input
                  id="footer-text"
                  value={settings.branding.footerText}
                  onChange={(e) => updateSetting('branding', 'footerText', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-css">Custom CSS</Label>
                <Textarea
                  id="custom-css"
                  value={settings.branding.customCSS}
                  onChange={(e) => updateSetting('branding', 'customCSS', e.target.value)}
                  rows={6}
                  placeholder="/* Add your custom CSS here */"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Integrations Settings */}
        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Third-party Integrations</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4>Calendar & Meeting Integrations</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="google-calendar">Google Calendar</Label>
                      <p className="text-sm text-muted-foreground">Sync appointments with Google Calendar</p>
                    </div>
                    <Switch
                      id="google-calendar"
                      checked={settings.integrations.googleCalendar}
                      onCheckedChange={(checked) => updateSetting('integrations', 'googleCalendar', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="zoom-integration">Zoom Integration</Label>
                      <p className="text-sm text-muted-foreground">Generate Zoom meetings for consultations</p>
                    </div>
                    <Switch
                      id="zoom-integration"
                      checked={settings.integrations.zoomIntegration}
                      onCheckedChange={(checked) => updateSetting('integrations', 'zoomIntegration', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4>Communication Providers</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email-provider">Email Provider</Label>
                    <Select 
                      value={settings.integrations.emailProvider} 
                      onValueChange={(value) => updateSetting('integrations', 'emailProvider', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sendgrid">SendGrid</SelectItem>
                        <SelectItem value="mailgun">Mailgun</SelectItem>
                        <SelectItem value="aws-ses">AWS SES</SelectItem>
                        <SelectItem value="smtp">Custom SMTP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sms-provider">SMS Provider</Label>
                    <Select 
                      value={settings.integrations.smsProvider} 
                      onValueChange={(value) => updateSetting('integrations', 'smsProvider', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="twilio">Twilio</SelectItem>
                        <SelectItem value="aws-sns">AWS SNS</SelectItem>
                        <SelectItem value="textlocal">TextLocal</SelectItem>
                        <SelectItem value="msg91">MSG91</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4>Payment & Other Integrations</h4>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="payment-gateway">Payment Gateway</Label>
                    <Select 
                      value={settings.integrations.paymentGateway} 
                      onValueChange={(value) => updateSetting('integrations', 'paymentGateway', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="razorpay">Razorpay</SelectItem>
                        <SelectItem value="stripe">Stripe</SelectItem>
                        <SelectItem value="paytm">Paytm</SelectItem>
                        <SelectItem value="paypal">PayPal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="whatsapp-api">WhatsApp API</Label>
                      <p className="text-sm text-muted-foreground">Send notifications via WhatsApp</p>
                    </div>
                    <Switch
                      id="whatsapp-api"
                      checked={settings.integrations.whatsappApi}
                      onCheckedChange={(checked) => updateSetting('integrations', 'whatsappApi', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}