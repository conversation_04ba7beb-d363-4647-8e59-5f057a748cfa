const { validationResult } = require('express-validator');
const Consultation = require('../models/Consultation');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const Settings = require('../models/Settings');

// @desc    Get all notifications for a user
// @route   GET /api/notifications
// @access  Private
const getNotifications = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, type, status = 'unread' } = req.query;
    const userId = req.user.id;

    // Mock notifications - in a real app, you'd have a Notification model
    const notifications = await generateNotifications(userId, type, status);

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedNotifications = notifications.slice(startIndex, endIndex);

    res.status(200).json({
      success: true,
      count: paginatedNotifications.length,
      total: notifications.length,
      pagination: {
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(notifications.length / limitNum),
      },
      data: paginatedNotifications,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Mark notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
const markAsRead = async (req, res, next) => {
  try {
    const { id } = req.params;

    // In a real app, you'd update the notification in the database
    res.status(200).json({
      success: true,
      message: 'Notification marked as read',
      data: {
        id,
        status: 'read',
        readAt: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/mark-all-read
// @access  Private
const markAllAsRead = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // In a real app, you'd update all unread notifications for the user
    res.status(200).json({
      success: true,
      message: 'All notifications marked as read',
      data: {
        userId,
        markedAt: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Send appointment reminder notifications
// @route   POST /api/notifications/appointment-reminders
// @access  Private (Admin only)
const sendAppointmentReminders = async (req, res, next) => {
  try {
    const settings = await Settings.getSettings();
    const reminderTime = settings.notifications.reminderTime || 24; // hours

    // Get upcoming consultations within reminder time
    const reminderDate = new Date();
    reminderDate.setHours(reminderDate.getHours() + reminderTime);

    const upcomingConsultations = await Consultation.find({
      appointmentDate: {
        $gte: new Date(),
        $lte: reminderDate,
      },
      status: 'Scheduled',
    })
      .populate('patient', 'name email phone')
      .populate('doctor', 'name specialization');

    const reminders = [];

    for (const consultation of upcomingConsultations) {
      // Create reminder notifications
      const reminder = {
        id: `reminder_${consultation._id}`,
        type: 'appointment_reminder',
        title: 'Upcoming Appointment',
        message: `You have an appointment with ${consultation.doctor.name} on ${consultation.appointmentDate.toDateString()} at ${consultation.appointmentTime}`,
        recipient: {
          id: consultation.patient._id,
          name: consultation.patient.name,
          email: consultation.patient.email,
          phone: consultation.patient.phone,
        },
        consultation: {
          id: consultation._id,
          doctor: consultation.doctor.name,
          date: consultation.appointmentDate,
          time: consultation.appointmentTime,
        },
        createdAt: new Date(),
        status: 'sent',
      };

      reminders.push(reminder);

      // Here you would integrate with email/SMS services
      console.log(`Reminder sent to ${consultation.patient.name} for appointment with ${consultation.doctor.name}`);
    }

    res.status(200).json({
      success: true,
      message: `Sent ${reminders.length} appointment reminders`,
      data: {
        remindersSent: reminders.length,
        reminders,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get notification statistics
// @route   GET /api/notifications/stats
// @access  Private
const getNotificationStats = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Mock stats - in a real app, you'd query the Notification model
    const stats = {
      total: 45,
      unread: 12,
      read: 33,
      byType: {
        appointment_reminder: 15,
        payment_alert: 8,
        system_alert: 12,
        doctor_alert: 10,
      },
      recent: await generateNotifications(userId, null, null, 5),
    };

    res.status(200).json({
      success: true,
      data: stats,
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to generate mock notifications
const generateNotifications = async (userId, type, status, limit = null) => {
  const notifications = [
    {
      id: 'notif_1',
      type: 'appointment_reminder',
      title: 'Upcoming Appointment',
      message: 'You have an appointment with Dr. Rajesh Kumar tomorrow at 10:00 AM',
      status: 'unread',
      priority: 'high',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      data: {
        consultationId: 'consultation_123',
        doctorName: 'Dr. Rajesh Kumar',
        appointmentTime: '10:00 AM',
      },
    },
    {
      id: 'notif_2',
      type: 'payment_alert',
      title: 'Payment Received',
      message: 'Payment of ₹500 received for consultation with Dr. Priya Sharma',
      status: 'read',
      priority: 'medium',
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      data: {
        amount: 500,
        consultationId: 'consultation_124',
        doctorName: 'Dr. Priya Sharma',
      },
    },
    {
      id: 'notif_3',
      type: 'system_alert',
      title: 'System Maintenance',
      message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM',
      status: 'unread',
      priority: 'low',
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      data: {
        maintenanceStart: '2:00 AM',
        maintenanceEnd: '4:00 AM',
      },
    },
    {
      id: 'notif_4',
      type: 'doctor_alert',
      title: 'New Doctor Registration',
      message: 'Dr. Amit Patel has registered and is pending approval',
      status: 'unread',
      priority: 'medium',
      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      data: {
        doctorId: 'doctor_125',
        doctorName: 'Dr. Amit Patel',
        specialization: 'Kayachikitsa',
      },
    },
    {
      id: 'notif_5',
      type: 'appointment_reminder',
      title: 'Appointment Cancelled',
      message: 'Your appointment with Dr. Rajesh Kumar has been cancelled',
      status: 'read',
      priority: 'high',
      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      data: {
        consultationId: 'consultation_125',
        doctorName: 'Dr. Rajesh Kumar',
        reason: 'Doctor unavailable',
      },
    },
  ];

  let filtered = notifications;

  // Filter by type
  if (type) {
    filtered = filtered.filter(n => n.type === type);
  }

  // Filter by status
  if (status) {
    filtered = filtered.filter(n => n.status === status);
  }

  // Limit results
  if (limit) {
    filtered = filtered.slice(0, limit);
  }

  return filtered;
};

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  sendAppointmentReminders,
  getNotificationStats,
};
