import { useState } from "react";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { LoginForm } from "./components/LoginForm";
import { AdminSidebar } from "./components/AdminSidebar";
import { DashboardOverview } from "./components/DashboardOverview";
import { ConsultationsManager } from "./components/ConsultationsManager";
import { DoctorsManager } from "./components/DoctorsManager";
import { SlotManagement } from "./components/SlotManagement";
import { PatientsManager } from "./components/PatientsManager";
import { ConsultationTypesManager } from "./components/ConsultationTypesManager";
import { AnalyticsReports } from "./components/AnalyticsReports";
import { SettingsManager } from "./components/SettingsManager";
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card";

function AppContent() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'consultations':
        return <ConsultationsManager />;
      case 'doctors':
        return <DoctorsManager />;
      case 'slot-management':
        return <SlotManagement />;
      case 'patients':
        return <PatientsManager />;
      case 'consultation-types':
        return <ConsultationTypesManager />;
      case 'analytics':
        return <AnalyticsReports />;
      // case 'revenue':
      //   return <RevenuePlaceholder />;
      case 'settings':
        return <SettingsManager />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar activeTab={activeTab} onTabChange={setActiveTab} />
      <main className="flex-1 overflow-auto">
        {renderContent()}
      </main>
    </div>
  );
}

export default function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

// Placeholder components for tabs not yet implemented

function RevenuePlaceholder() {
  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Revenue Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Revenue tracking and financial reports...</p>
        </CardContent>
      </Card>
    </div>
  );
}