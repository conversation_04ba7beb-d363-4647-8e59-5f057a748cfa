# Ayura Admin Panel Backend

A Node.js/Express backend server for the Ayura Admin Panel - an Ayurvedic consultation management system.

## Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **RESTful API**: Complete CRUD operations for all entities
- **Database Integration**: MongoDB with Mongoose ODM
- **Security**: Helmet, CORS, rate limiting, and input validation
- **Data Models**: Users, Doctors, Patients, Consultations, Settings
- **Analytics**: Dashboard statistics and reporting endpoints

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

## Installation

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your configuration.

4. Seed the database with sample data:
   ```bash
   npm run seed
   ```

## Running the Server

### Development Mode
```bash
npm run dev
```
The server will start on `http://localhost:5000` with auto-reload enabled.

### Production Mode
```bash
npm start
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user profile
- `PUT /api/auth/profile` - Update user profile
- `PUT /api/auth/change-password` - Change password

### Doctors
- `GET /api/doctors` - Get all doctors
- `POST /api/doctors` - Create new doctor (Admin only)
- `GET /api/doctors/:id` - Get doctor by ID
- `PUT /api/doctors/:id` - Update doctor (Admin only)
- `DELETE /api/doctors/:id` - Delete doctor (Admin only)
- `GET /api/doctors/:id/stats` - Get doctor statistics

### Patients
- `GET /api/patients` - Get all patients
- `POST /api/patients` - Create new patient
- `GET /api/patients/:id` - Get patient by ID
- `PUT /api/patients/:id` - Update patient
- `DELETE /api/patients/:id` - Delete patient (Admin only)
- `GET /api/patients/:id/stats` - Get patient statistics

### Consultations
- `GET /api/consultations` - Get all consultations
- `POST /api/consultations` - Create new consultation
- `GET /api/consultations/:id` - Get consultation by ID
- `PUT /api/consultations/:id` - Update consultation
- `DELETE /api/consultations/:id` - Delete consultation (Admin only)
- `PUT /api/consultations/:id/status` - Update consultation status
- `GET /api/consultations/doctor/:doctorId` - Get consultations by doctor
- `GET /api/consultations/patient/:patientId` - Get consultations by patient

### Slots
- `GET /api/slots/available` - Get available time slots
- `GET /api/slots/doctor/:doctorId` - Get doctor's schedule
- `POST /api/slots/generate` - Generate time slots (Admin only)

### Settings
- `GET /api/settings` - Get platform settings (Admin only)
- `PUT /api/settings` - Update platform settings (Admin only)

### Analytics
- `GET /api/analytics/dashboard` - Get dashboard statistics
- `GET /api/analytics/revenue` - Get revenue analytics (Admin only)
- `GET /api/analytics/consultations` - Get consultation analytics
- `GET /api/analytics/doctors` - Get doctor performance analytics
- `GET /api/analytics/patients` - Get patient analytics

### Search
- `GET /api/search?q=:query&type=:type` - Global search across all entities
- `GET /api/search/doctors/advanced` - Advanced doctor search with filters
- `GET /api/search/patients/advanced` - Advanced patient search with filters
- `GET /api/search/consultations/advanced` - Advanced consultation search with filters

### Bulk Operations
- `POST /api/bulk/doctors` - Bulk import doctors (Admin only)
- `POST /api/bulk/patients` - Bulk import patients (Admin only)
- `PUT /api/bulk/consultations/status` - Bulk update consultation status
- `DELETE /api/bulk/consultations` - Bulk delete consultations (Admin only)
- `GET /api/bulk/export/:type` - Export data (doctors, patients, consultations) (Admin only)

### Notifications
- `GET /api/notifications` - Get user notifications
- `GET /api/notifications/stats` - Get notification statistics
- `PUT /api/notifications/:id/read` - Mark notification as read
- `PUT /api/notifications/mark-all-read` - Mark all notifications as read
- `POST /api/notifications/appointment-reminders` - Send appointment reminders (Admin only)

### Reports
- `GET /api/reports/consultations` - Generate consultation reports (Admin only)
- `GET /api/reports/doctors` - Generate doctor performance reports (Admin only)
- `GET /api/reports/revenue` - Generate revenue reports (Admin only)

### File Upload
- `POST /api/upload` - Upload file
- `POST /api/upload/avatar` - Upload avatar/profile picture
- `GET /api/upload/files/:filename` - Download file
- `GET /api/upload/avatars/:filename` - Download avatar
- `DELETE /api/upload/files/:filename` - Delete file
- `GET /api/upload/stats` - Get upload statistics (Admin only)

### System Monitoring
- `GET /api/system/health` - Get system health status (Admin only)
- `GET /api/system/metrics` - Get system performance metrics (Admin only)
- `GET /api/system/logs` - Get system logs (Admin only)
- `POST /api/system/cache/clear` - Clear system cache (Admin only)

## Authentication

All API endpoints (except registration and login) require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Demo Credentials

After running the seed script, you can use these credentials:

- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin

## Database Schema

### User
- name, email, password, role, phone, avatar, isActive, lastLogin

### Doctor
- name, email, phone, specialization, experience, qualification, registrationNumber, consultationFee, bio, languages, availability, rating, totalConsultations

### Patient
- name, email, phone, dateOfBirth, gender, address, emergencyContact, medicalHistory, allergies, currentMedications, bloodGroup, height, weight

### Consultation
- patient, doctor, appointmentDate, appointmentTime, duration, type, mode, status, chiefComplaint, symptoms, diagnosis, treatment, vitals, followUpDate, notes, fee, rating, documents

### Settings
- platform, notifications, security, payments, branding, integrations

## Error Handling

The API uses consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [] // Validation errors if applicable
}
```

## Security Features

- JWT authentication
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation and sanitization
- Role-based access control

## Environment Variables

```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/ayura_admin
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3001
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with sample data
- `npm test` - Test core APIs
- `npm run test:edit-delete` - Test edit and delete operations
- `npm run test:pending` - Test newly implemented APIs

## Health Check

Check if the server is running:
```
GET /health
```

Response:
```json
{
  "status": "OK",
  "message": "Ayura Admin Backend Server is running",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "development"
}
```

## New APIs Documentation

For detailed documentation of the newly implemented APIs (Notifications, Reports, Upload, System Monitoring), see [PENDING_APIS_DOCUMENTATION.md](./PENDING_APIS_DOCUMENTATION.md).

## License

This project is licensed under the MIT License.
