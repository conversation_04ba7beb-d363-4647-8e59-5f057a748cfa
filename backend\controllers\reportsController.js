const { validationResult } = require('express-validator');
const Consultation = require('../models/Consultation');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');

// @desc    Generate consultation report
// @route   GET /api/reports/consultations
// @access  Private (Admin only)
const getConsultationReport = async (req, res, next) => {
  try {
    const { startDate, endDate, doctorId, status, format = 'json' } = req.query;

    // Build query
    const query = {};
    
    if (startDate || endDate) {
      query.appointmentDate = {};
      if (startDate) query.appointmentDate.$gte = new Date(startDate);
      if (endDate) query.appointmentDate.$lte = new Date(endDate);
    }
    
    if (doctorId) query.doctor = doctorId;
    if (status) query.status = status;

    // Get consultations with populated data
    const consultations = await Consultation.find(query)
      .populate('patient', 'name email phone age gender')
      .populate('doctor', 'name specialization')
      .sort({ appointmentDate: -1 });

    // Generate report statistics
    const stats = {
      totalConsultations: consultations.length,
      statusBreakdown: {},
      revenueTotal: 0,
      averageRating: 0,
      doctorBreakdown: {},
      monthlyTrends: {},
    };

    // Calculate statistics
    consultations.forEach(consultation => {
      // Status breakdown
      stats.statusBreakdown[consultation.status] = 
        (stats.statusBreakdown[consultation.status] || 0) + 1;

      // Revenue calculation
      if (consultation.fee && consultation.fee.amount) {
        stats.revenueTotal += consultation.fee.amount;
      }

      // Doctor breakdown
      const doctorName = consultation.doctor.name;
      if (!stats.doctorBreakdown[doctorName]) {
        stats.doctorBreakdown[doctorName] = {
          consultations: 0,
          revenue: 0,
          specialization: consultation.doctor.specialization,
        };
      }
      stats.doctorBreakdown[doctorName].consultations += 1;
      if (consultation.fee && consultation.fee.amount) {
        stats.doctorBreakdown[doctorName].revenue += consultation.fee.amount;
      }

      // Monthly trends
      const month = consultation.appointmentDate.toISOString().slice(0, 7); // YYYY-MM
      if (!stats.monthlyTrends[month]) {
        stats.monthlyTrends[month] = { consultations: 0, revenue: 0 };
      }
      stats.monthlyTrends[month].consultations += 1;
      if (consultation.fee && consultation.fee.amount) {
        stats.monthlyTrends[month].revenue += consultation.fee.amount;
      }

      // Average rating
      if (consultation.rating) {
        stats.averageRating += consultation.rating;
      }
    });

    // Calculate average rating
    const ratedConsultations = consultations.filter(c => c.rating).length;
    if (ratedConsultations > 0) {
      stats.averageRating = stats.averageRating / ratedConsultations;
    }

    const reportData = {
      reportType: 'consultations',
      generatedAt: new Date(),
      filters: { startDate, endDate, doctorId, status },
      statistics: stats,
      data: consultations,
    };

    if (format === 'csv') {
      // Generate CSV format
      const csvData = generateConsultationCSV(consultations);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="consultation_report.csv"');
      res.send(csvData);
    } else {
      res.status(200).json({
        success: true,
        data: reportData,
      });
    }
  } catch (error) {
    next(error);
  }
};

// @desc    Generate doctor performance report
// @route   GET /api/reports/doctors
// @access  Private (Admin only)
const getDoctorReport = async (req, res, next) => {
  try {
    const { startDate, endDate, specialization, format = 'json' } = req.query;

    // Build query for consultations
    const consultationQuery = {};
    if (startDate || endDate) {
      consultationQuery.appointmentDate = {};
      if (startDate) consultationQuery.appointmentDate.$gte = new Date(startDate);
      if (endDate) consultationQuery.appointmentDate.$lte = new Date(endDate);
    }

    // Get all doctors
    const doctorQuery = {};
    if (specialization) doctorQuery.specialization = specialization;
    
    const doctors = await Doctor.find(doctorQuery);

    const doctorReports = [];

    for (const doctor of doctors) {
      const consultations = await Consultation.find({
        ...consultationQuery,
        doctor: doctor._id,
      });

      const report = {
        doctor: {
          id: doctor._id,
          name: doctor.name,
          specialization: doctor.specialization,
          experience: doctor.experience,
          consultationFee: doctor.consultationFee,
        },
        performance: {
          totalConsultations: consultations.length,
          completedConsultations: consultations.filter(c => c.status === 'Completed').length,
          cancelledConsultations: consultations.filter(c => c.status === 'Cancelled').length,
          noShowConsultations: consultations.filter(c => c.status === 'No Show').length,
          totalRevenue: consultations.reduce((sum, c) => sum + (c.fee?.amount || 0), 0),
          averageRating: 0,
          patientSatisfaction: 0,
        },
      };

      // Calculate ratings
      const ratedConsultations = consultations.filter(c => c.rating);
      if (ratedConsultations.length > 0) {
        report.performance.averageRating = 
          ratedConsultations.reduce((sum, c) => sum + c.rating, 0) / ratedConsultations.length;
        report.performance.patientSatisfaction = 
          (ratedConsultations.filter(c => c.rating >= 4).length / ratedConsultations.length) * 100;
      }

      doctorReports.push(report);
    }

    // Sort by total consultations
    doctorReports.sort((a, b) => b.performance.totalConsultations - a.performance.totalConsultations);

    const reportData = {
      reportType: 'doctors',
      generatedAt: new Date(),
      filters: { startDate, endDate, specialization },
      summary: {
        totalDoctors: doctorReports.length,
        totalConsultations: doctorReports.reduce((sum, d) => sum + d.performance.totalConsultations, 0),
        totalRevenue: doctorReports.reduce((sum, d) => sum + d.performance.totalRevenue, 0),
        averageRating: doctorReports.reduce((sum, d) => sum + d.performance.averageRating, 0) / doctorReports.length,
      },
      data: doctorReports,
    };

    if (format === 'csv') {
      const csvData = generateDoctorCSV(doctorReports);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="doctor_report.csv"');
      res.send(csvData);
    } else {
      res.status(200).json({
        success: true,
        data: reportData,
      });
    }
  } catch (error) {
    next(error);
  }
};

// @desc    Generate revenue report
// @route   GET /api/reports/revenue
// @access  Private (Admin only)
const getRevenueReport = async (req, res, next) => {
  try {
    const { startDate, endDate, groupBy = 'month', format = 'json' } = req.query;

    const query = { status: 'Completed' };
    if (startDate || endDate) {
      query.appointmentDate = {};
      if (startDate) query.appointmentDate.$gte = new Date(startDate);
      if (endDate) query.appointmentDate.$lte = new Date(endDate);
    }

    const consultations = await Consultation.find(query)
      .populate('doctor', 'name specialization')
      .sort({ appointmentDate: 1 });

    const revenueData = {};
    let totalRevenue = 0;

    consultations.forEach(consultation => {
      const amount = consultation.fee?.amount || 0;
      totalRevenue += amount;

      let groupKey;
      const date = consultation.appointmentDate;

      switch (groupBy) {
        case 'day':
          groupKey = date.toISOString().slice(0, 10); // YYYY-MM-DD
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          groupKey = weekStart.toISOString().slice(0, 10);
          break;
        case 'month':
          groupKey = date.toISOString().slice(0, 7); // YYYY-MM
          break;
        case 'year':
          groupKey = date.getFullYear().toString();
          break;
        default:
          groupKey = date.toISOString().slice(0, 7);
      }

      if (!revenueData[groupKey]) {
        revenueData[groupKey] = {
          period: groupKey,
          revenue: 0,
          consultations: 0,
          averagePerConsultation: 0,
        };
      }

      revenueData[groupKey].revenue += amount;
      revenueData[groupKey].consultations += 1;
    });

    // Calculate averages
    Object.values(revenueData).forEach(period => {
      period.averagePerConsultation = period.consultations > 0 
        ? period.revenue / period.consultations 
        : 0;
    });

    const reportData = {
      reportType: 'revenue',
      generatedAt: new Date(),
      filters: { startDate, endDate, groupBy },
      summary: {
        totalRevenue,
        totalConsultations: consultations.length,
        averagePerConsultation: consultations.length > 0 ? totalRevenue / consultations.length : 0,
        periods: Object.keys(revenueData).length,
      },
      data: Object.values(revenueData).sort((a, b) => a.period.localeCompare(b.period)),
    };

    res.status(200).json({
      success: true,
      data: reportData,
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to generate consultation CSV
const generateConsultationCSV = (consultations) => {
  const headers = [
    'Date', 'Time', 'Patient Name', 'Doctor Name', 'Type', 'Status', 
    'Fee', 'Rating', 'Chief Complaint'
  ];
  
  const rows = consultations.map(c => [
    c.appointmentDate.toISOString().slice(0, 10),
    c.appointmentTime,
    c.patient.name,
    c.doctor.name,
    c.type,
    c.status,
    c.fee?.amount || 0,
    c.rating || '',
    c.chiefComplaint || ''
  ]);

  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

// Helper function to generate doctor CSV
const generateDoctorCSV = (doctorReports) => {
  const headers = [
    'Doctor Name', 'Specialization', 'Total Consultations', 'Completed', 
    'Cancelled', 'No Show', 'Total Revenue', 'Average Rating', 'Patient Satisfaction %'
  ];
  
  const rows = doctorReports.map(d => [
    d.doctor.name,
    d.doctor.specialization,
    d.performance.totalConsultations,
    d.performance.completedConsultations,
    d.performance.cancelledConsultations,
    d.performance.noShowConsultations,
    d.performance.totalRevenue,
    d.performance.averageRating.toFixed(2),
    d.performance.patientSatisfaction.toFixed(1)
  ]);

  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

module.exports = {
  getConsultationReport,
  getDoctorReport,
  getRevenueReport,
};
