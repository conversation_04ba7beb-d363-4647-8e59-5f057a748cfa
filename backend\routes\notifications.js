const express = require('express');
const {
  getNotifications,
  markAsR<PERSON>,
  markAllAsRead,
  sendAppointmentReminders,
  getNotificationStats,
} = require('../controllers/notificationController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Routes
router.get('/', getNotifications);
router.get('/stats', getNotificationStats);
router.put('/:id/read', markAsRead);
router.put('/mark-all-read', markAllAsRead);
router.post('/appointment-reminders', authorize('admin'), sendAppointmentReminders);

module.exports = router;
