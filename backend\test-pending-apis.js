const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5000/api';
let authToken = '';

// Test authentication first
const testAuthentication = async () => {
  try {
    console.log('🔐 Testing Authentication...');
    
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
    });

    if (response.data.success && response.data.token) {
      authToken = response.data.token;
      console.log('✅ Authentication successful');
      return true;
    } else {
      console.log('❌ Authentication failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Authentication error:', error.response?.data?.message || error.message);
    return false;
  }
};

// Test notifications API
const testNotifications = async () => {
  try {
    console.log('\n📢 Testing Notifications API...');

    // Get all notifications
    const notificationsResponse = await axios.get(`${BASE_URL}/notifications`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ Get notifications:', notificationsResponse.data.data.length, 'notifications');

    // Get notification stats
    const statsResponse = await axios.get(`${BASE_URL}/notifications/stats`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ Get notification stats:', statsResponse.data.data.total, 'total notifications');

    // Mark notification as read
    const markReadResponse = await axios.put(`${BASE_URL}/notifications/notif_1/read`, {}, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ Mark notification as read:', markReadResponse.data.message);

    // Send appointment reminders
    const remindersResponse = await axios.post(`${BASE_URL}/notifications/appointment-reminders`, {}, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ Send appointment reminders:', remindersResponse.data.message);

  } catch (error) {
    console.log('❌ Notifications API error:', error.response?.data?.message || error.message);
  }
};

// Test reports API
const testReports = async () => {
  try {
    console.log('\n📊 Testing Reports API...');

    // Get consultation report
    const consultationReport = await axios.get(`${BASE_URL}/reports/consultations`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params: {
        startDate: '2025-01-01',
        endDate: '2025-12-31',
      },
    });
    console.log('✅ Consultation report:', consultationReport.data.data.statistics.totalConsultations, 'consultations');

    // Get doctor report
    const doctorReport = await axios.get(`${BASE_URL}/reports/doctors`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params: {
        startDate: '2025-01-01',
        endDate: '2025-12-31',
      },
    });
    console.log('✅ Doctor report:', doctorReport.data.data.summary.totalDoctors, 'doctors analyzed');

    // Get revenue report
    const revenueReport = await axios.get(`${BASE_URL}/reports/revenue`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params: {
        startDate: '2025-01-01',
        endDate: '2025-12-31',
        groupBy: 'month',
      },
    });
    console.log('✅ Revenue report:', `₹${revenueReport.data.data.summary.totalRevenue}`, 'total revenue');

    // Test CSV export
    const csvReport = await axios.get(`${BASE_URL}/reports/consultations`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params: {
        format: 'csv',
        startDate: '2025-01-01',
        endDate: '2025-12-31',
      },
    });
    console.log('✅ CSV export:', csvReport.data.length, 'characters in CSV');

  } catch (error) {
    console.log('❌ Reports API error:', error.response?.data?.message || error.message);
  }
};

// Test upload API
const testUpload = async () => {
  try {
    console.log('\n📁 Testing Upload API...');

    // Create a test file
    const testContent = 'This is a test file for upload API testing.';
    const testFilePath = path.join(__dirname, 'test-upload.txt');
    fs.writeFileSync(testFilePath, testContent);

    // Test file upload
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', fs.createReadStream(testFilePath));

    const uploadResponse = await axios.post(`${BASE_URL}/upload`, form, {
      headers: {
        ...form.getHeaders(),
        Authorization: `Bearer ${authToken}`,
      },
    });
    console.log('✅ File upload:', uploadResponse.data.data.originalName);

    const uploadedFilename = uploadResponse.data.data.filename;

    // Test file download
    const downloadResponse = await axios.get(`${BASE_URL}/upload/files/${uploadedFilename}`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ File download:', downloadResponse.data.length, 'characters');

    // Test upload stats
    const statsResponse = await axios.get(`${BASE_URL}/upload/stats`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ Upload stats:', statsResponse.data.data.totalFiles, 'total files');

    // Test file deletion
    const deleteResponse = await axios.delete(`${BASE_URL}/upload/files/${uploadedFilename}`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ File deletion:', deleteResponse.data.message);

    // Clean up test file
    fs.unlinkSync(testFilePath);

  } catch (error) {
    console.log('❌ Upload API error:', error.response?.data?.message || error.message);
  }
};

// Test system API
const testSystem = async () => {
  try {
    console.log('\n🖥️ Testing System API...');

    // Get system health
    const healthResponse = await axios.get(`${BASE_URL}/system/health`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ System health:', healthResponse.data.data.status.overall);

    // Get system metrics
    const metricsResponse = await axios.get(`${BASE_URL}/system/metrics`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params: { timeframe: '24h' },
    });
    console.log('✅ System metrics:', metricsResponse.data.data.performance.memory.usagePercent.toFixed(1) + '% memory usage');

    // Get system logs
    const logsResponse = await axios.get(`${BASE_URL}/system/logs`, {
      headers: { Authorization: `Bearer ${authToken}` },
      params: { level: 'all', limit: 10 },
    });
    console.log('✅ System logs:', logsResponse.data.data.logs.length, 'log entries');

    // Clear cache
    const cacheResponse = await axios.post(`${BASE_URL}/system/cache/clear`, {}, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log('✅ Clear cache:', cacheResponse.data.message);

  } catch (error) {
    console.log('❌ System API error:', error.response?.data?.message || error.message);
  }
};

// Test health endpoint
const testHealthEndpoint = async () => {
  try {
    console.log('\n❤️ Testing Health Endpoint...');

    const healthResponse = await axios.get('http://localhost:5000/health');
    console.log('✅ Health check:', healthResponse.data.status, '-', healthResponse.data.message);

  } catch (error) {
    console.log('❌ Health endpoint error:', error.response?.data?.message || error.message);
  }
};

// Main test runner
const runAllTests = async () => {
  console.log('🚀 Starting Pending APIs Tests for Ayura Admin Backend\n');
  console.log('=' .repeat(60));

  const authSuccess = await testAuthentication();
  
  if (!authSuccess) {
    console.log('\n❌ Authentication failed. Stopping tests.');
    return;
  }

  await testNotifications();
  await testReports();
  await testUpload();
  await testSystem();
  await testHealthEndpoint();

  console.log('\n' + '='.repeat(60));
  console.log('🎉 Pending APIs Tests Completed!');
  console.log('\n📋 Summary of New APIs Implemented:');
  console.log('   📢 Notifications API - Get, mark as read, send reminders');
  console.log('   📊 Reports API - Consultation, doctor, and revenue reports');
  console.log('   📁 Upload API - File upload, download, and management');
  console.log('   🖥️ System API - Health monitoring, metrics, and logs');
  console.log('   ❤️ Enhanced Health Endpoint - System status monitoring');
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests };
