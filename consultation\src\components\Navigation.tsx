import { Leaf, User, LogOut } from "lucide-react";
import { Button } from "./ui/button";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

interface NavigationProps {
  currentPage: PageType;
  onNavigate: (page: PageType) => void;
  isLoggedIn: boolean;
  onLogout: () => void;
}

export function Navigation({ currentPage, onNavigate, isLoggedIn, onLogout }: NavigationProps) {
  const navItems = [
    { id: 'home' as const, label: 'Home' },
    { id: 'consultations' as const, label: 'Consultations' },
    { id: 'book' as const, label: 'Book Consultation' },
    { id: 'about' as const, label: 'About Us' },
    { id: 'contact' as const, label: 'Contact Us' }
  ];

  return (
    <nav className="border-b bg-white/90 backdrop-blur-sm px-6 py-4 sticky top-0 z-50">
      <div className="mx-auto flex max-w-7xl items-center justify-between">
        <div 
          className="flex items-center space-x-2 cursor-pointer"
          onClick={() => onNavigate('home')}
        >
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-green-600 to-green-500 rounded-full">
            <Leaf className="h-5 w-5 text-white" />
          </div>
          <span className="text-xl font-semibold text-gray-900">Ayura</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-6">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onNavigate(item.id)}
              className={`px-3 py-2 rounded-md transition-colors ${
                currentPage === item.id
                  ? 'text-green-600 bg-green-50 font-medium'
                  : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
              }`}
            >
              {item.label}
            </button>
          ))}
        </div>

        <div className="flex items-center space-x-3">
          {isLoggedIn ? (
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 text-gray-700">
                <User className="w-4 h-4" />
                <span className="text-sm">Welcome back!</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={onLogout}
                className="flex items-center space-x-1"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigate('login')}
              >
                Login
              </Button>
              <Button
                size="sm"
                onClick={() => onNavigate('signup')}
                className="bg-green-600 hover:bg-green-700"
              >
                Sign Up
              </Button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}