// API configuration and utilities for Ayura Admin Panel

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Get auth token from localStorage
const getAuthToken = () => {
  return localStorage.getItem('authToken');
};

// Set auth token in localStorage
const setAuthToken = (token) => {
  localStorage.setItem('authToken', token);
};

// Remove auth token from localStorage
const removeAuthToken = () => {
  localStorage.removeItem('authToken');
};

// API request wrapper with error handling
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getAuthToken();

  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }

    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

// Auth API calls
export const authAPI = {
  login: async (credentials) => {
    const response = await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    if (response.success && response.token) {
      setAuthToken(response.token);
    }
    
    return response;
  },

  register: async (userData) => {
    const response = await apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    
    if (response.success && response.token) {
      setAuthToken(response.token);
    }
    
    return response;
  },

  logout: () => {
    removeAuthToken();
    return Promise.resolve({ success: true });
  },

  getProfile: () => apiRequest('/auth/me'),

  updateProfile: (profileData) => apiRequest('/auth/profile', {
    method: 'PUT',
    body: JSON.stringify(profileData),
  }),

  changePassword: (passwordData) => apiRequest('/auth/change-password', {
    method: 'PUT',
    body: JSON.stringify(passwordData),
  }),
};

// Doctors API calls
export const doctorsAPI = {
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/doctors${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id) => apiRequest(`/doctors/${id}`),

  create: (doctorData) => apiRequest('/doctors', {
    method: 'POST',
    body: JSON.stringify(doctorData),
  }),

  update: (id, doctorData) => apiRequest(`/doctors/${id}`, {
    method: 'PUT',
    body: JSON.stringify(doctorData),
  }),

  delete: (id) => apiRequest(`/doctors/${id}`, {
    method: 'DELETE',
  }),

  getStats: (id) => apiRequest(`/doctors/${id}/stats`),
};

// Patients API calls
export const patientsAPI = {
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/patients${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id) => apiRequest(`/patients/${id}`),

  create: (patientData) => apiRequest('/patients', {
    method: 'POST',
    body: JSON.stringify(patientData),
  }),

  update: (id, patientData) => apiRequest(`/patients/${id}`, {
    method: 'PUT',
    body: JSON.stringify(patientData),
  }),

  delete: (id) => apiRequest(`/patients/${id}`, {
    method: 'DELETE',
  }),

  getStats: (id) => apiRequest(`/patients/${id}/stats`),
};

// Consultations API calls
export const consultationsAPI = {
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/consultations${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id) => apiRequest(`/consultations/${id}`),

  create: (consultationData) => apiRequest('/consultations', {
    method: 'POST',
    body: JSON.stringify(consultationData),
  }),

  update: (id, consultationData) => apiRequest(`/consultations/${id}`, {
    method: 'PUT',
    body: JSON.stringify(consultationData),
  }),

  updateStatus: (id, status) => apiRequest(`/consultations/${id}/status`, {
    method: 'PUT',
    body: JSON.stringify({ status }),
  }),

  delete: (id) => apiRequest(`/consultations/${id}`, {
    method: 'DELETE',
  }),

  getByDoctor: (doctorId) => apiRequest(`/consultations/doctor/${doctorId}`),

  getByPatient: (patientId) => apiRequest(`/consultations/patient/${patientId}`),
};

// Slots API calls
export const slotsAPI = {
  // Test endpoint for debugging
  test: (data) => apiRequest('/slots/test', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  // Get all slots with optional filtering
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/slots${queryString ? `?${queryString}` : ''}`);
  },

  // Get available slots with filtering
  getAvailable: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/slots/available?${queryString}`);
  },

  // Get slots for a specific doctor
  getDoctorSlots: (doctorId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/slots/doctor/${doctorId}${queryString ? `?${queryString}` : ''}`);
  },

  // Get a specific slot by ID
  getById: (slotId) => apiRequest(`/slots/${slotId}`),

  // Create a single slot
  create: (slotData) => apiRequest('/slots', {
    method: 'POST',
    body: JSON.stringify(slotData),
  }),

  // Generate multiple slots (bulk creation)
  generateSlots: (slotData) => apiRequest('/slots/generate', {
    method: 'POST',
    body: JSON.stringify(slotData),
  }),

  // Update an existing slot
  update: (slotId, slotData) => apiRequest(`/slots/${slotId}`, {
    method: 'PUT',
    body: JSON.stringify(slotData),
  }),

  // Delete a slot
  delete: (slotId) => apiRequest(`/slots/${slotId}`, {
    method: 'DELETE',
  }),

  // Update slot status (available, blocked, cancelled)
  updateStatus: (slotId, status) => apiRequest(`/slots/${slotId}/status`, {
    method: 'PATCH',
    body: JSON.stringify({ status }),
  }),

  // Book a slot for a patient
  book: (slotId, bookingData) => apiRequest(`/slots/${slotId}/book`, {
    method: 'POST',
    body: JSON.stringify(bookingData),
  }),

  // Cancel a booking
  cancelBooking: (slotId) => apiRequest(`/slots/${slotId}/cancel`, {
    method: 'POST',
  }),

  // Get slot statistics
  getStats: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/slots/stats${queryString ? `?${queryString}` : ''}`);
  },

  // Check slot availability for a specific time
  checkAvailability: (doctorId, date, startTime, duration) => {
    const params = new URLSearchParams({
      doctorId,
      date,
      startTime,
      duration: duration.toString()
    });
    return apiRequest(`/slots/check-availability?${params}`);
  },

  // Get slots by date range
  getByDateRange: (startDate, endDate, params = {}) => {
    const queryParams = new URLSearchParams({
      startDate,
      endDate,
      ...params
    });
    return apiRequest(`/slots/date-range?${queryParams}`);
  },
};

// Settings API calls
export const settingsAPI = {
  get: () => apiRequest('/settings'),

  update: (settingsData) => apiRequest('/settings', {
    method: 'PUT',
    body: JSON.stringify(settingsData),
  }),
};

// Search API calls
export const searchAPI = {
  global: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/search?${queryString}`);
  },

  doctors: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/search/doctors/advanced?${queryString}`);
  },

  patients: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/search/patients/advanced?${queryString}`);
  },

  consultations: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/search/consultations/advanced?${queryString}`);
  },
};

// Bulk operations API calls
export const bulkAPI = {
  importDoctors: (doctors) => apiRequest('/bulk/doctors', {
    method: 'POST',
    body: JSON.stringify({ doctors }),
  }),

  importPatients: (patients) => apiRequest('/bulk/patients', {
    method: 'POST',
    body: JSON.stringify({ patients }),
  }),

  updateConsultationStatus: (consultationIds, status) => apiRequest('/bulk/consultations/status', {
    method: 'PUT',
    body: JSON.stringify({ consultationIds, status }),
  }),

  deleteConsultations: (consultationIds) => apiRequest('/bulk/consultations', {
    method: 'DELETE',
    body: JSON.stringify({ consultationIds }),
  }),

  exportData: (type, format = 'json') => {
    const queryString = new URLSearchParams({ format }).toString();
    return apiRequest(`/bulk/export/${type}?${queryString}`);
  },
};

// Analytics API calls
export const analyticsAPI = {
  getDashboardStats: () => apiRequest('/analytics/dashboard'),

  getRevenueAnalytics: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/analytics/revenue?${queryString}`);
  },

  getConsultationAnalytics: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/analytics/consultations?${queryString}`);
  },

  getDoctorPerformance: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/analytics/doctors?${queryString}`);
  },

  getPatientAnalytics: (params) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/analytics/patients?${queryString}`);
  },
};

// Notifications API calls
export const notificationsAPI = {
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/notifications${queryString ? `?${queryString}` : ''}`);
  },

  getStats: () => apiRequest('/notifications/stats'),

  markAsRead: (id) => apiRequest(`/notifications/${id}/read`, {
    method: 'PUT',
  }),

  markAllAsRead: () => apiRequest('/notifications/mark-all-read', {
    method: 'PUT',
  }),

  sendAppointmentReminders: () => apiRequest('/notifications/appointment-reminders', {
    method: 'POST',
  }),
};

// Reports API calls
export const reportsAPI = {
  getConsultationReport: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/reports/consultations${queryString ? `?${queryString}` : ''}`);
  },

  getDoctorReport: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/reports/doctors${queryString ? `?${queryString}` : ''}`);
  },

  getRevenueReport: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/reports/revenue${queryString ? `?${queryString}` : ''}`);
  },
};

// Upload API calls
export const uploadAPI = {
  uploadFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return apiRequest('/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        // Don't set Content-Type, let browser set it with boundary
      },
    });
  },

  uploadAvatar: (file) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return apiRequest('/upload/avatar', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
      },
    });
  },

  getFile: (filename) => `${API_BASE_URL}/upload/files/${filename}`,
  getAvatar: (filename) => `${API_BASE_URL}/upload/avatars/${filename}`,

  deleteFile: (filename) => apiRequest(`/upload/files/${filename}`, {
    method: 'DELETE',
  }),

  getStats: () => apiRequest('/upload/stats'),
};

// System API calls
export const systemAPI = {
  getHealth: () => apiRequest('/system/health'),

  getMetrics: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/system/metrics${queryString ? `?${queryString}` : ''}`);
  },

  getLogs: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiRequest(`/system/logs${queryString ? `?${queryString}` : ''}`);
  },

  clearCache: () => apiRequest('/system/cache/clear', {
    method: 'POST',
  }),
};

// Health check
export const healthAPI = {
  check: () => fetch(`${API_BASE_URL.replace('/api', '')}/health`).then(res => res.json()),
};

// Export utility functions
export { getAuthToken, setAuthToken, removeAuthToken };
