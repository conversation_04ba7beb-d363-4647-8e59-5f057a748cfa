import { useState } from "react";
import { Navigation } from "./components/Navigation";
import { Homepage } from "./components/pages/Homepage";
import { ConsultationsList } from "./components/pages/ConsultationsList";
import { BookConsultation } from "./components/pages/BookConsultation";
import { AboutUs } from "./components/pages/AboutUs";
import { ContactUs } from "./components/pages/ContactUs";
import { Login } from "./components/pages/Login";
import { Signup } from "./components/pages/Signup";
import { Toaster } from "./components/ui/sonner";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

export default function App() {
  const [currentPage, setCurrentPage] = useState<PageType>('home');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <Homepage onNavigate={setCurrentPage} />;
      case 'consultations':
        return <ConsultationsList onNavigate={setCurrentPage} />;
      case 'book':
        return <BookConsultation onNavigate={setCurrentPage} />;
      case 'about':
        return <AboutUs onNavigate={setCurrentPage} />;
      case 'contact':
        return <ContactUs onNavigate={setCurrentPage} />;
      case 'login':
        return <Login onNavigate={setCurrentPage} onLogin={setIsLoggedIn} />;
      case 'signup':
        return <Signup onNavigate={setCurrentPage} onLogin={setIsLoggedIn} />;
      default:
        return <Homepage onNavigate={setCurrentPage} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-amber-50">
      <Navigation 
        currentPage={currentPage} 
        onNavigate={setCurrentPage} 
        isLoggedIn={isLoggedIn}
        onLogout={() => setIsLoggedIn(false)}
      />
      {renderPage()}
      <Toaster />
    </div>
  );
}