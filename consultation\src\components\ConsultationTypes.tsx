import { Card } from "./ui/card";
import { Check } from "lucide-react";

interface ConsultationType {
  id: string;
  title: string;
  duration: string;
  price: number;
  description: string;
  popular?: boolean;
}

const consultationTypes: ConsultationType[] = [
  {
    id: "general",
    title: "General Consultation",
    duration: "30 min",
    price: 1500,
    description: "Comprehensive health assessment and wellness guidance"
  },
  {
    id: "diet",
    title: "Diet & Nutrition",
    duration: "45 min", 
    price: 2000,
    description: "Personalized dietary plans based on your dosha",
    popular: true
  },
  {
    id: "lifestyle",
    title: "Lifestyle Coaching",
    duration: "60 min",
    price: 2500,
    description: "Holistic lifestyle recommendations and daily routines"
  },
  {
    id: "chronic",
    title: "Chronic Conditions",
    duration: "60 min",
    price: 3000,
    description: "Specialized care for long-term health issues"
  },
  {
    id: "wellness",
    title: "Wellness Check-up",
    duration: "30 min",
    price: 1200,
    description: "Preventive care and health maintenance"
  }
];

interface ConsultationTypesProps {
  selectedType: string | null;
  onTypeSelect: (typeId: string) => void;
}

export function ConsultationTypes({ selectedType, onTypeSelect }: ConsultationTypesProps) {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Consultation Type</h2>
      
      {consultationTypes.map((type) => (
        <Card 
          key={type.id}
          className={`p-4 cursor-pointer transition-all border-2 relative ${
            selectedType === type.id 
              ? 'border-green-600 bg-green-50' 
              : 'border-gray-200 hover:border-green-300'
          }`}
          onClick={() => onTypeSelect(type.id)}
        >
          {type.popular && (
            <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
              Popular
            </div>
          )}
          
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-medium text-gray-900">{type.title}</h3>
            {selectedType === type.id && (
              <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                <Check className="w-3 h-3 text-white" />
              </div>
            )}
          </div>
          
          <p className="text-sm text-gray-600 mb-3">{type.description}</p>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">{type.duration}</span>
            <span className="font-semibold text-green-600">₹{type.price}</span>
          </div>
        </Card>
      ))}
    </div>
  );
}