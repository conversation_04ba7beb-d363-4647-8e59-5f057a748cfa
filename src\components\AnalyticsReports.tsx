import { useState } from "react";
import { CalendarDays, Download, TrendingUp, TrendingDown, Users, IndianRupee, Clock, Star, Filter, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Badge } from "./ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";
import { DatePickerWithRange } from "./ui/date-picker-with-range";
import { Separator } from "./ui/separator";

// Mock data for analytics
const consultationTrend = [
  { month: 'Jan', consultations: 120, revenue: 180000, newPatients: 45 },
  { month: 'Feb', consultations: 135, revenue: 202500, newPatients: 52 },
  { month: 'Mar', consultations: 150, revenue: 225000, newPatients: 58 },
  { month: 'Apr', consultations: 142, revenue: 213000, newPatients: 48 },
  { month: 'May', consultations: 168, revenue: 252000, newPatients: 65 },
  { month: 'Jun', consultations: 185, revenue: 277500, newPatients: 72 },
  { month: 'Jul', consultations: 195, revenue: 292500, newPatients: 68 },
  { month: 'Aug', consultations: 210, revenue: 315000, newPatients: 78 }
];

const consultationTypes = [
  { name: 'General Consultation', value: 45, color: '#93a580' },
  { name: 'Specialized', value: 25, color: '#b8c6a5' },
  { name: 'Follow-up', value: 20, color: '#7a9167' },
  { name: 'Package', value: 10, color: '#5f7450' }
];

const doctorPerformance = [
  { doctor: 'Dr. Priya Sharma', consultations: 125, rating: 4.9, revenue: 187500, patients: 89 },
  { doctor: 'Dr. Arjun Patel', consultations: 98, rating: 4.8, revenue: 147000, patients: 72 },
  { doctor: 'Dr. Lakshmi Iyer', consultations: 87, rating: 4.7, revenue: 130500, patients: 64 },
  { doctor: 'Dr. Vikram Singh', consultations: 112, rating: 4.6, revenue: 168000, patients: 78 },
  { doctor: 'Dr. Meera Gupta', consultations: 95, rating: 4.8, revenue: 142500, patients: 68 }
];

const hourlyDistribution = [
  { hour: '9 AM', consultations: 12 },
  { hour: '10 AM', consultations: 18 },
  { hour: '11 AM', consultations: 25 },
  { hour: '12 PM', consultations: 20 },
  { hour: '1 PM', consultations: 8 },
  { hour: '2 PM', consultations: 15 },
  { hour: '3 PM', consultations: 28 },
  { hour: '4 PM', consultations: 32 },
  { hour: '5 PM', consultations: 25 },
  { hour: '6 PM', consultations: 18 },
  { hour: '7 PM', consultations: 12 }
];

const patientDemographics = [
  { ageGroup: '18-25', male: 45, female: 65 },
  { ageGroup: '26-35', male: 78, female: 95 },
  { ageGroup: '36-45', male: 68, female: 82 },
  { ageGroup: '46-55', male: 52, female: 58 },
  { ageGroup: '56+', male: 35, female: 42 }
];

export function AnalyticsReports() {
  const [dateRange, setDateRange] = useState("last30days");
  const [selectedMetric, setSelectedMetric] = useState("consultations");

  const kpiData = {
    totalConsultations: { value: 1245, change: 12.5, trend: 'up' },
    totalRevenue: { value: 1867500, change: 8.3, trend: 'up' },
    activePatients: { value: 856, change: -2.1, trend: 'down' },
    avgRating: { value: 4.7, change: 0.2, trend: 'up' },
    completionRate: { value: 94.2, change: 1.8, trend: 'up' },
    newPatients: { value: 245, change: 15.6, trend: 'up' }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => `${value}%`;

  const TrendIcon = ({ trend }: { trend: 'up' | 'down' }) => {
    return trend === 'up' ? 
      <TrendingUp className="w-4 h-4 text-green-600" /> : 
      <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  const KPICard = ({ title, value, change, trend, icon: Icon, formatter }: {
    title: string;
    value: number;
    change: number;
    trend: 'up' | 'down';
    icon: any;
    formatter?: (value: number) => string;
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="w-8 h-8 text-primary" />
            <div>
              <p className="text-sm text-muted-foreground">{title}</p>
              <p className="text-2xl">{formatter ? formatter(value) : value.toLocaleString()}</p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <TrendIcon trend={trend} />
            <span className={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {Math.abs(change)}%
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1>Analytics & Reports</h1>
          <p className="text-muted-foreground">Comprehensive insights and performance analytics</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <Calendar className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="last7days">Last 7 days</SelectItem>
              <SelectItem value="last30days">Last 30 days</SelectItem>
              <SelectItem value="last90days">Last 90 days</SelectItem>
              <SelectItem value="lastyear">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <KPICard
          title="Total Consultations"
          value={kpiData.totalConsultations.value}
          change={kpiData.totalConsultations.change}
          trend={kpiData.totalConsultations.trend}
          icon={CalendarDays}
        />
        <KPICard
          title="Total Revenue"
          value={kpiData.totalRevenue.value}
          change={kpiData.totalRevenue.change}
          trend={kpiData.totalRevenue.trend}
          icon={IndianRupee}
          formatter={formatCurrency}
        />
        <KPICard
          title="Active Patients"
          value={kpiData.activePatients.value}
          change={kpiData.activePatients.change}
          trend={kpiData.activePatients.trend}
          icon={Users}
        />
        <KPICard
          title="Average Rating"
          value={kpiData.avgRating.value}
          change={kpiData.avgRating.change}
          trend={kpiData.avgRating.trend}
          icon={Star}
        />
        <KPICard
          title="Completion Rate"
          value={kpiData.completionRate.value}
          change={kpiData.completionRate.change}
          trend={kpiData.completionRate.trend}
          icon={Clock}
          formatter={formatPercentage}
        />
        <KPICard
          title="New Patients"
          value={kpiData.newPatients.value}
          change={kpiData.newPatients.change}
          trend={kpiData.newPatients.trend}
          icon={Users}
        />
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="consultations">Consultations</TabsTrigger>
          <TabsTrigger value="doctors">Doctors</TabsTrigger>
          <TabsTrigger value="patients">Patients</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Consultation Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Consultation Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={consultationTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="consultations" 
                      stackId="1" 
                      stroke="#93a580" 
                      fill="#93a580" 
                      fillOpacity={0.6}
                      name="Consultations"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Consultation Types Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Consultation Types</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={consultationTypes}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {consultationTypes.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Hourly Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Peak Hours Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={hourlyDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="consultations" fill="#93a580" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Consultations Tab */}
        <TabsContent value="consultations" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Consultation Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <LineChart data={consultationTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="consultations" 
                      stroke="#93a580" 
                      strokeWidth={3}
                      dot={{ fill: '#93a580', strokeWidth: 2, r: 6 }}
                      name="Consultations"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="newPatients" 
                      stroke="#7a9167" 
                      strokeWidth={2}
                      dot={{ fill: '#7a9167', strokeWidth: 2, r: 4 }}
                      name="New Patients"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Consultation Status Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                    <span>Completed</span>
                    <Badge className="bg-green-100 text-green-700">94.2%</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                    <span>Scheduled</span>
                    <Badge className="bg-yellow-100 text-yellow-700">3.8%</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                    <span>Cancelled</span>
                    <Badge className="bg-red-100 text-red-700">1.5%</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span>No Show</span>
                    <Badge className="bg-gray-100 text-gray-700">0.5%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Doctors Tab */}
        <TabsContent value="doctors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Doctor Performance Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {doctorPerformance.map((doctor, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium">{doctor.doctor}</h3>
                        <p className="text-sm text-muted-foreground">{doctor.patients} patients</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <p className="font-medium">{doctor.consultations}</p>
                        <p className="text-muted-foreground">Consultations</p>
                      </div>
                      <div className="text-center">
                        <p className="font-medium">{formatCurrency(doctor.revenue)}</p>
                        <p className="text-muted-foreground">Revenue</p>
                      </div>
                      <div className="text-center">
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span className="font-medium">{doctor.rating}</span>
                        </div>
                        <p className="text-muted-foreground">Rating</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Patients Tab */}
        <TabsContent value="patients" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Patient Demographics</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <BarChart data={patientDemographics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="ageGroup" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="male" stackId="a" fill="#93a580" name="Male" />
                    <Bar dataKey="female" stackId="a" fill="#b8c6a5" name="Female" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Patient Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <AreaChart data={consultationTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="newPatients" 
                      stroke="#93a580" 
                      fill="#93a580" 
                      fillOpacity={0.6}
                      name="New Patients"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Revenue Tab */}
        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <AreaChart data={consultationTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `₹${value/1000}K`} />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Area 
                      type="monotone" 
                      dataKey="revenue" 
                      stroke="#93a580" 
                      fill="#93a580" 
                      fillOpacity={0.6}
                      name="Revenue"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-primary/5 rounded-lg">
                    <span>General Consultations</span>
                    <span className="font-medium">₹8,47,500</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-primary/5 rounded-lg">
                    <span>Specialized Consultations</span>
                    <span className="font-medium">₹6,75,000</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-primary/5 rounded-lg">
                    <span>Follow-up Sessions</span>
                    <span className="font-medium">₹2,40,000</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-primary/5 rounded-lg">
                    <span>Wellness Packages</span>
                    <span className="font-medium">₹1,05,000</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center p-3 bg-primary/10 rounded-lg">
                    <span className="font-medium">Total Revenue</span>
                    <span className="font-medium text-lg">₹18,67,500</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}