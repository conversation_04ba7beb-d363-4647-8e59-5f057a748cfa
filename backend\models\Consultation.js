const mongoose = require('mongoose');

const consultationSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Please add patient'],
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Doctor',
    required: [true, 'Please add doctor'],
  },
  appointmentDate: {
    type: Date,
    required: [true, 'Please add appointment date'],
  },
  appointmentTime: {
    type: String,
    required: [true, 'Please add appointment time'],
  },
  duration: {
    type: Number,
    default: 30, // minutes
  },
  type: {
    type: String,
    required: [true, 'Please add consultation type'],
    enum: ['Initial Consultation', 'Follow-up', 'Panchakarma', 'General Checkup', 'Emergency'],
  },
  mode: {
    type: String,
    required: [true, 'Please add consultation mode'],
    enum: ['In-person', 'Video Call', 'Phone Call'],
    default: 'In-person',
  },
  status: {
    type: String,
    enum: ['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'],
    default: 'Scheduled',
  },
  chiefComplaint: {
    type: String,
    maxlength: [500, 'Chief complaint cannot be more than 500 characters'],
  },
  symptoms: [{
    symptom: String,
    severity: {
      type: String,
      enum: ['Mild', 'Moderate', 'Severe'],
    },
    duration: String,
  }],
  diagnosis: {
    primary: String,
    secondary: [String],
    notes: String,
  },
  treatment: {
    medications: [{
      name: String,
      dosage: String,
      frequency: String,
      duration: String,
      instructions: String,
    }],
    therapies: [{
      name: String,
      duration: String,
      frequency: String,
      instructions: String,
    }],
    lifestyle: [{
      category: {
        type: String,
        enum: ['Diet', 'Exercise', 'Sleep', 'Stress Management', 'Other'],
      },
      recommendation: String,
    }],
  },
  vitals: {
    bloodPressure: {
      systolic: Number,
      diastolic: Number,
    },
    heartRate: Number,
    temperature: Number,
    weight: Number,
    height: Number,
    bmi: Number,
  },
  followUpDate: {
    type: Date,
  },
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot be more than 1000 characters'],
  },
  fee: {
    amount: {
      type: Number,
      required: [true, 'Please add consultation fee'],
    },
    currency: {
      type: String,
      default: 'INR',
    },
    paymentStatus: {
      type: String,
      enum: ['Pending', 'Paid', 'Refunded', 'Failed'],
      default: 'Pending',
    },
    paymentMethod: {
      type: String,
      enum: ['Cash', 'Card', 'UPI', 'Net Banking', 'Wallet'],
    },
    transactionId: String,
  },
  rating: {
    score: {
      type: Number,
      min: 1,
      max: 5,
    },
    feedback: String,
  },
  documents: [{
    name: String,
    url: String,
    type: {
      type: String,
      enum: ['Prescription', 'Report', 'Image', 'Other'],
    },
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
  }],
}, {
  timestamps: true,
});

// Index for efficient queries
consultationSchema.index({ patient: 1, appointmentDate: 1 });
consultationSchema.index({ doctor: 1, appointmentDate: 1 });
consultationSchema.index({ status: 1, appointmentDate: 1 });

module.exports = mongoose.model('Consultation', consultationSchema);
