const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const Consultation = require('../models/Consultation');

// @desc    Get dashboard statistics
// @route   GET /api/analytics/dashboard
// @access  Private
const getDashboardStats = async (req, res, next) => {
  try {
    // Get current date ranges
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Total counts
    const totalDoctors = await Doctor.countDocuments({ isActive: true });
    const totalPatients = await Patient.countDocuments({ isActive: true });
    const totalConsultations = await Consultation.countDocuments();

    // Today's stats
    const todayConsultations = await Consultation.countDocuments({
      appointmentDate: { $gte: startOfToday },
    });

    const todayRevenue = await Consultation.aggregate([
      {
        $match: {
          appointmentDate: { $gte: startOfToday },
          'fee.paymentStatus': 'Paid',
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$fee.amount' },
        },
      },
    ]);

    // Upcoming consultations
    const upcomingConsultations = await Consultation.countDocuments({
      appointmentDate: { $gte: new Date() },
      status: 'Scheduled',
    });

    // Recent consultations
    const recentConsultations = await Consultation.find()
      .populate('patient', 'name')
      .populate('doctor', 'name')
      .sort({ createdAt: -1 })
      .limit(5);

    // Consultation status distribution
    const statusDistribution = await Consultation.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: {
          totalDoctors,
          totalPatients,
          totalConsultations,
          upcomingConsultations,
        },
        today: {
          consultations: todayConsultations,
          revenue: todayRevenue.length > 0 ? todayRevenue[0].total : 0,
        },
        recentConsultations,
        statusDistribution,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get revenue analytics
// @route   GET /api/analytics/revenue
// @access  Private (Admin only)
const getRevenueAnalytics = async (req, res, next) => {
  try {
    const { period = 'month' } = req.query;
    
    let groupBy;
    let dateRange;
    const now = new Date();

    switch (period) {
      case 'week':
        groupBy = { $dayOfWeek: '$appointmentDate' };
        dateRange = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'month':
        groupBy = { $dayOfMonth: '$appointmentDate' };
        dateRange = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        groupBy = { $month: '$appointmentDate' };
        dateRange = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        groupBy = { $dayOfMonth: '$appointmentDate' };
        dateRange = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    const revenueData = await Consultation.aggregate([
      {
        $match: {
          appointmentDate: { $gte: dateRange },
          'fee.paymentStatus': 'Paid',
        },
      },
      {
        $group: {
          _id: groupBy,
          revenue: { $sum: '$fee.amount' },
          consultations: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    // Total revenue
    const totalRevenue = await Consultation.aggregate([
      {
        $match: {
          'fee.paymentStatus': 'Paid',
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$fee.amount' },
        },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        period,
        revenueData,
        totalRevenue: totalRevenue.length > 0 ? totalRevenue[0].total : 0,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get consultation analytics
// @route   GET /api/analytics/consultations
// @access  Private
const getConsultationAnalytics = async (req, res, next) => {
  try {
    // Consultation types distribution
    const typeDistribution = await Consultation.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
        },
      },
    ]);

    // Consultation modes distribution
    const modeDistribution = await Consultation.aggregate([
      {
        $group: {
          _id: '$mode',
          count: { $sum: 1 },
        },
      },
    ]);

    // Monthly consultation trends
    const monthlyTrends = await Consultation.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$appointmentDate' },
            month: { $month: '$appointmentDate' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 },
      },
      {
        $limit: 12,
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        typeDistribution,
        modeDistribution,
        monthlyTrends,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get doctor performance analytics
// @route   GET /api/analytics/doctors
// @access  Private
const getDoctorPerformance = async (req, res, next) => {
  try {
    // Top doctors by consultation count
    const topDoctorsByConsultations = await Consultation.aggregate([
      {
        $group: {
          _id: '$doctor',
          consultations: { $sum: 1 },
          revenue: {
            $sum: {
              $cond: [
                { $eq: ['$fee.paymentStatus', 'Paid'] },
                '$fee.amount',
                0,
              ],
            },
          },
        },
      },
      {
        $lookup: {
          from: 'doctors',
          localField: '_id',
          foreignField: '_id',
          as: 'doctor',
        },
      },
      {
        $unwind: '$doctor',
      },
      {
        $project: {
          name: '$doctor.name',
          specialization: '$doctor.specialization',
          consultations: 1,
          revenue: 1,
        },
      },
      {
        $sort: { consultations: -1 },
      },
      {
        $limit: 10,
      },
    ]);

    // Doctor ratings
    const doctorRatings = await Consultation.aggregate([
      {
        $match: {
          'rating.score': { $exists: true },
        },
      },
      {
        $group: {
          _id: '$doctor',
          averageRating: { $avg: '$rating.score' },
          totalRatings: { $sum: 1 },
        },
      },
      {
        $lookup: {
          from: 'doctors',
          localField: '_id',
          foreignField: '_id',
          as: 'doctor',
        },
      },
      {
        $unwind: '$doctor',
      },
      {
        $project: {
          name: '$doctor.name',
          averageRating: { $round: ['$averageRating', 1] },
          totalRatings: 1,
        },
      },
      {
        $sort: { averageRating: -1 },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        topDoctorsByConsultations,
        doctorRatings,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get patient analytics
// @route   GET /api/analytics/patients
// @access  Private
const getPatientAnalytics = async (req, res, next) => {
  try {
    // New patients this month
    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const newPatientsThisMonth = await Patient.countDocuments({
      registrationDate: { $gte: startOfMonth },
    });

    // Patient age distribution
    const patients = await Patient.find({}, 'dateOfBirth');
    const ageDistribution = {
      '0-18': 0,
      '19-35': 0,
      '36-50': 0,
      '51-65': 0,
      '65+': 0,
    };

    patients.forEach(patient => {
      const age = new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear();
      if (age <= 18) ageDistribution['0-18']++;
      else if (age <= 35) ageDistribution['19-35']++;
      else if (age <= 50) ageDistribution['36-50']++;
      else if (age <= 65) ageDistribution['51-65']++;
      else ageDistribution['65+']++;
    });

    // Patient gender distribution
    const genderDistribution = await Patient.aggregate([
      {
        $group: {
          _id: '$gender',
          count: { $sum: 1 },
        },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        newPatientsThisMonth,
        ageDistribution,
        genderDistribution,
      },
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getDashboardStats,
  getRevenueAnalytics,
  getConsultationAnalytics,
  getDoctorPerformance,
  getPatientAnalytics,
};
