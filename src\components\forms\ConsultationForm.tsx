import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Consultation, ConsultationFormData, FormErrors, Doctor, Patient } from '../../types/models';
import { doctorsAPI, patientsAPI } from '../../utils/api';

interface ConsultationFormProps {
  consultation?: Consultation;
  onSubmit: (data: ConsultationFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const consultationTypes = [
  'Initial Consultation',
  'Follow-up',
  'Panchakarma',
  'General Checkup',
  'Emergency'
];

const consultationStatuses = [
  'Scheduled',
  'In Progress', 
  'Completed',
  'Cancelled',
  'No Show'
];

export function ConsultationForm({ consultation, onSubmit, onCancel, isLoading = false }: ConsultationFormProps) {
  const [formData, setFormData] = useState<ConsultationFormData>({
    patientId: consultation?.patient._id || '',
    doctorId: consultation?.doctor._id || '',
    appointmentDate: consultation?.appointmentDate || '',
    appointmentTime: consultation?.appointmentTime || '',
    type: consultation?.type || '',
    mode: consultation?.mode || 'In-person',
    status: consultation?.status || 'Scheduled',
    fee: {
      amount: consultation?.fee?.amount || 0,
      currency: consultation?.fee?.currency || 'INR'
    },
    symptoms: consultation?.symptoms || '',
    diagnosis: consultation?.diagnosis || '',
    prescription: consultation?.prescription || '',
    notes: consultation?.notes || '',
    followUpDate: consultation?.followUpDate || ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  useEffect(() => {
    fetchDoctorsAndPatients();
  }, []);

  const fetchDoctorsAndPatients = async () => {
    try {
      setLoadingData(true);
      const [doctorsResponse, patientsResponse] = await Promise.all([
        doctorsAPI.getAll(),
        patientsAPI.getAll()
      ]);
      
      if (doctorsResponse.success) setDoctors(doctorsResponse.data);
      if (patientsResponse.success) setPatients(patientsResponse.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.patientId) newErrors.patientId = 'Patient is required';
    if (!formData.doctorId) newErrors.doctorId = 'Doctor is required';
    if (!formData.appointmentDate) newErrors.appointmentDate = 'Appointment date is required';
    if (!formData.appointmentTime) newErrors.appointmentTime = 'Appointment time is required';
    if (!formData.type) newErrors.type = 'Consultation type is required';
    if (formData.fee.amount < 0) newErrors.fee = 'Fee must be positive';

    // Date validation - appointment should be in the future for new consultations
    if (!consultation && formData.appointmentDate) {
      const appointmentDateTime = new Date(`${formData.appointmentDate}T${formData.appointmentTime}`);
      const now = new Date();
      if (appointmentDateTime <= now) {
        newErrors.appointmentDate = 'Appointment must be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  const handleInputChange = (field: keyof ConsultationFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleFeeChange = (field: 'amount' | 'currency', value: any) => {
    setFormData(prev => ({
      ...prev,
      fee: { ...prev.fee, [field]: value }
    }));
    if (errors.fee) {
      setErrors(prev => ({ ...prev, fee: '' }));
    }
  };

  const selectedDoctor = doctors.find(d => d._id === formData.doctorId);
  const selectedPatient = patients.find(p => p._id === formData.patientId);

  // Auto-fill fee when doctor is selected
  useEffect(() => {
    if (selectedDoctor && !consultation) {
      setFormData(prev => ({
        ...prev,
        fee: { ...prev.fee, amount: selectedDoctor.consultationFee }
      }));
    }
  }, [selectedDoctor, consultation]);

  if (loadingData) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>Loading doctors and patients...</p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Consultation Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="patientId">Patient *</Label>
              <Select
                value={formData.patientId}
                onValueChange={(value) => handleInputChange('patientId', value)}
              >
                <SelectTrigger className={errors.patientId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select patient" />
                </SelectTrigger>
                <SelectContent>
                  {patients.map((patient) => (
                    <SelectItem key={patient._id} value={patient._id}>
                      {patient.name} - {patient.phone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.patientId && <p className="text-red-500 text-sm mt-1">{errors.patientId}</p>}
            </div>

            <div>
              <Label htmlFor="doctorId">Doctor *</Label>
              <Select
                value={formData.doctorId}
                onValueChange={(value) => handleInputChange('doctorId', value)}
              >
                <SelectTrigger className={errors.doctorId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select doctor" />
                </SelectTrigger>
                <SelectContent>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor._id} value={doctor._id}>
                      {doctor.name} - {doctor.specialization}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.doctorId && <p className="text-red-500 text-sm mt-1">{errors.doctorId}</p>}
            </div>

            <div>
              <Label htmlFor="appointmentDate">Appointment Date *</Label>
              <Input
                id="appointmentDate"
                type="date"
                value={formData.appointmentDate}
                onChange={(e) => handleInputChange('appointmentDate', e.target.value)}
                className={errors.appointmentDate ? 'border-red-500' : ''}
              />
              {errors.appointmentDate && <p className="text-red-500 text-sm mt-1">{errors.appointmentDate}</p>}
            </div>

            <div>
              <Label htmlFor="appointmentTime">Appointment Time *</Label>
              <Input
                id="appointmentTime"
                type="time"
                value={formData.appointmentTime}
                onChange={(e) => handleInputChange('appointmentTime', e.target.value)}
                className={errors.appointmentTime ? 'border-red-500' : ''}
              />
              {errors.appointmentTime && <p className="text-red-500 text-sm mt-1">{errors.appointmentTime}</p>}
            </div>

            <div>
              <Label htmlFor="type">Consultation Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange('type', value)}
              >
                <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {consultationTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.type && <p className="text-red-500 text-sm mt-1">{errors.type}</p>}
            </div>

            <div>
              <Label htmlFor="mode">Mode</Label>
              <Select
                value={formData.mode}
                onValueChange={(value) => handleInputChange('mode', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="In-person">In-person</SelectItem>
                  <SelectItem value="Video Call">Video Call</SelectItem>
                  <SelectItem value="Phone Call">Phone Call</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value as any)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {consultationStatuses.map((status) => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="fee">Consultation Fee (₹) *</Label>
              <Input
                id="fee"
                type="number"
                min="0"
                value={formData.fee.amount}
                onChange={(e) => handleFeeChange('amount', parseInt(e.target.value) || 0)}
                className={errors.fee ? 'border-red-500' : ''}
              />
              {errors.fee && <p className="text-red-500 text-sm mt-1">{errors.fee}</p>}
            </div>
          </div>

          {formData.followUpDate && (
            <div>
              <Label htmlFor="followUpDate">Follow-up Date</Label>
              <Input
                id="followUpDate"
                type="date"
                value={formData.followUpDate}
                onChange={(e) => handleInputChange('followUpDate', e.target.value)}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Medical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Medical Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="symptoms">Symptoms</Label>
            <Textarea
              id="symptoms"
              value={formData.symptoms}
              onChange={(e) => handleInputChange('symptoms', e.target.value)}
              placeholder="Patient's symptoms and complaints..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="diagnosis">Diagnosis</Label>
            <Textarea
              id="diagnosis"
              value={formData.diagnosis}
              onChange={(e) => handleInputChange('diagnosis', e.target.value)}
              placeholder="Doctor's diagnosis..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="prescription">Prescription</Label>
            <Textarea
              id="prescription"
              value={formData.prescription}
              onChange={(e) => handleInputChange('prescription', e.target.value)}
              placeholder="Prescribed medications and treatments..."
              rows={4}
            />
          </div>

          <div>
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Any additional notes or observations..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="followUpDate">Follow-up Date</Label>
            <Input
              id="followUpDate"
              type="date"
              value={formData.followUpDate}
              onChange={(e) => handleInputChange('followUpDate', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Selected Patient/Doctor Info */}
      {(selectedPatient || selectedDoctor) && (
        <Card>
          <CardHeader>
            <CardTitle>Selected Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedPatient && (
                <div>
                  <h4 className="font-medium mb-2">Patient Information</h4>
                  <div className="text-sm space-y-1">
                    <p><span className="font-medium">Name:</span> {selectedPatient.name}</p>
                    <p><span className="font-medium">Age:</span> {selectedPatient.age}</p>
                    <p><span className="font-medium">Phone:</span> {selectedPatient.phone}</p>
                    <p><span className="font-medium">Email:</span> {selectedPatient.email}</p>
                  </div>
                </div>
              )}

              {selectedDoctor && (
                <div>
                  <h4 className="font-medium mb-2">Doctor Information</h4>
                  <div className="text-sm space-y-1">
                    <p><span className="font-medium">Name:</span> {selectedDoctor.name}</p>
                    <p><span className="font-medium">Specialization:</span> {selectedDoctor.specialization}</p>
                    <p><span className="font-medium">Experience:</span> {selectedDoctor.experience} years</p>
                    <p><span className="font-medium">Fee:</span> ₹{selectedDoctor.consultationFee}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : consultation ? 'Update Consultation' : 'Create Consultation'}
        </Button>
      </div>
    </form>
  );
}
