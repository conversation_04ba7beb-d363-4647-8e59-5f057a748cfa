{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "*", "cmdk": "^1.1.1", "date-fns": "*", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.487.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "*", "vaul": "^1.1.2"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-react-swc": "^3.10.2", "concurrently": "^8.2.2", "vite": "6.3.5"}, "scripts": {"dev": "vite", "build": "vite build", "dev:admin": "vite --port 3000", "dev:consultation": "npm --prefix consultation run dev", "dev:backend": "npm --prefix backend start", "dev:admin-backend": "concurrently --kill-others --names \"ADMIN,BACKEND\" --prefix-colors \"cyan,yellow\" \"vite --port 3000\" \"npm --prefix backend start\"", "dev:consultation-backend": "concurrently --kill-others --names \"CONSULTAT<PERSON>,BACKEND\" --prefix-colors \"green,yellow\" \"npm --prefix consultation run dev\" \"npm --prefix backend start\"", "dev:all": "concurrently --kill-others --names \"ADMI<PERSON>,CONSULTATION,BACKEND\" --prefix-colors \"cyan,green,yellow\" \"vite --port 3000\" \"npm --prefix consultation run dev\" \"npm --prefix backend start\"", "start": "npm run dev:all", "start:dev": "node start-dev.js"}}