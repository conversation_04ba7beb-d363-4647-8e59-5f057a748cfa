const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const Consultation = require('../models/Consultation');

// @desc    Global search across all entities
// @route   GET /api/search?q=:query&type=:type
// @access  Private
const globalSearch = async (req, res, next) => {
  try {
    const { q: query, type, limit = 10 } = req.query;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters long',
      });
    }

    const searchLimit = Math.min(parseInt(limit), 50); // Max 50 results
    const results = {};

    // Search doctors
    if (!type || type === 'doctors') {
      const doctors = await Doctor.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { specialization: { $regex: query, $options: 'i' } },
          { qualification: { $regex: query, $options: 'i' } },
          { registrationNumber: { $regex: query, $options: 'i' } },
        ],
        isActive: true,
      })
        .select('name email specialization qualification registrationNumber consultationFee rating')
        .limit(searchLimit);

      results.doctors = doctors;
    }

    // Search patients
    if (!type || type === 'patients') {
      const patients = await Patient.find({
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { phone: { $regex: query, $options: 'i' } },
        ],
        isActive: true,
      })
        .select('name email phone dateOfBirth gender totalConsultations')
        .limit(searchLimit);

      results.patients = patients;
    }

    // Search consultations
    if (!type || type === 'consultations') {
      const consultations = await Consultation.find({
        $or: [
          { chiefComplaint: { $regex: query, $options: 'i' } },
          { 'diagnosis.primary': { $regex: query, $options: 'i' } },
          { notes: { $regex: query, $options: 'i' } },
        ],
      })
        .populate('patient', 'name email')
        .populate('doctor', 'name specialization')
        .select('appointmentDate appointmentTime type status chiefComplaint diagnosis.primary')
        .sort({ appointmentDate: -1 })
        .limit(searchLimit);

      results.consultations = consultations;
    }

    // Calculate total results
    const totalResults = Object.values(results).reduce((sum, arr) => sum + arr.length, 0);

    res.status(200).json({
      success: true,
      query,
      totalResults,
      results,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Advanced doctor search with filters
// @route   GET /api/search/doctors/advanced
// @access  Private
const advancedDoctorSearch = async (req, res, next) => {
  try {
    const {
      name,
      specialization,
      experience,
      minFee,
      maxFee,
      rating,
      languages,
      availability,
      page = 1,
      limit = 10,
    } = req.query;

    const query = { isActive: true };

    // Name search
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    // Specialization filter
    if (specialization) {
      query.specialization = specialization;
    }

    // Experience filter
    if (experience) {
      query.experience = { $gte: parseInt(experience) };
    }

    // Fee range filter
    if (minFee || maxFee) {
      query.consultationFee = {};
      if (minFee) query.consultationFee.$gte = parseFloat(minFee);
      if (maxFee) query.consultationFee.$lte = parseFloat(maxFee);
    }

    // Rating filter
    if (rating) {
      query.rating = { $gte: parseFloat(rating) };
    }

    // Languages filter
    if (languages) {
      const languageArray = languages.split(',').map(lang => lang.trim());
      query.languages = { $in: languageArray };
    }

    // Availability filter (for specific day)
    if (availability) {
      const day = availability.toLowerCase();
      query[`availability.${day}.available`] = true;
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const doctors = await Doctor.find(query)
      .sort({ rating: -1, totalConsultations: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await Doctor.countDocuments(query);

    res.status(200).json({
      success: true,
      count: doctors.length,
      total,
      pagination: {
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum),
      },
      data: doctors,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Advanced patient search with filters
// @route   GET /api/search/patients/advanced
// @access  Private
const advancedPatientSearch = async (req, res, next) => {
  try {
    const {
      name,
      email,
      phone,
      gender,
      ageMin,
      ageMax,
      bloodGroup,
      hasAllergies,
      hasMedications,
      page = 1,
      limit = 10,
    } = req.query;

    const query = { isActive: true };

    // Name search
    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    // Email search
    if (email) {
      query.email = { $regex: email, $options: 'i' };
    }

    // Phone search
    if (phone) {
      query.phone = { $regex: phone, $options: 'i' };
    }

    // Gender filter
    if (gender) {
      query.gender = gender;
    }

    // Blood group filter
    if (bloodGroup) {
      query.bloodGroup = bloodGroup;
    }

    // Age range filter (requires aggregation)
    let pipeline = [{ $match: query }];

    if (ageMin || ageMax) {
      pipeline.push({
        $addFields: {
          age: {
            $floor: {
              $divide: [
                { $subtract: [new Date(), '$dateOfBirth'] },
                365.25 * 24 * 60 * 60 * 1000
              ]
            }
          }
        }
      });

      const ageQuery = {};
      if (ageMin) ageQuery.$gte = parseInt(ageMin);
      if (ageMax) ageQuery.$lte = parseInt(ageMax);
      
      pipeline.push({ $match: { age: ageQuery } });
    }

    // Allergies filter
    if (hasAllergies === 'true') {
      query.allergies = { $exists: true, $not: { $size: 0 } };
    } else if (hasAllergies === 'false') {
      query.$or = [
        { allergies: { $exists: false } },
        { allergies: { $size: 0 } }
      ];
    }

    // Medications filter
    if (hasMedications === 'true') {
      query.currentMedications = { $exists: true, $not: { $size: 0 } };
    } else if (hasMedications === 'false') {
      query.$or = [
        { currentMedications: { $exists: false } },
        { currentMedications: { $size: 0 } }
      ];
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Add pagination to pipeline
    pipeline.push(
      { $sort: { name: 1 } },
      { $skip: skip },
      { $limit: limitNum }
    );

    const patients = await Patient.aggregate(pipeline);
    const total = await Patient.countDocuments(query);

    res.status(200).json({
      success: true,
      count: patients.length,
      total,
      pagination: {
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum),
      },
      data: patients,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Search consultations with filters
// @route   GET /api/search/consultations/advanced
// @access  Private
const advancedConsultationSearch = async (req, res, next) => {
  try {
    const {
      patientName,
      doctorName,
      type,
      status,
      mode,
      startDate,
      endDate,
      diagnosis,
      page = 1,
      limit = 10,
    } = req.query;

    let pipeline = [];

    // Match stage
    const matchQuery = {};

    if (type) matchQuery.type = type;
    if (status) matchQuery.status = status;
    if (mode) matchQuery.mode = mode;
    if (diagnosis) matchQuery['diagnosis.primary'] = { $regex: diagnosis, $options: 'i' };

    // Date range filter
    if (startDate || endDate) {
      matchQuery.appointmentDate = {};
      if (startDate) matchQuery.appointmentDate.$gte = new Date(startDate);
      if (endDate) matchQuery.appointmentDate.$lte = new Date(endDate);
    }

    pipeline.push({ $match: matchQuery });

    // Lookup patient and doctor
    pipeline.push(
      {
        $lookup: {
          from: 'patients',
          localField: 'patient',
          foreignField: '_id',
          as: 'patientInfo'
        }
      },
      {
        $lookup: {
          from: 'doctors',
          localField: 'doctor',
          foreignField: '_id',
          as: 'doctorInfo'
        }
      },
      { $unwind: '$patientInfo' },
      { $unwind: '$doctorInfo' }
    );

    // Filter by patient or doctor name
    if (patientName || doctorName) {
      const nameMatchQuery = {};
      if (patientName) {
        nameMatchQuery['patientInfo.name'] = { $regex: patientName, $options: 'i' };
      }
      if (doctorName) {
        nameMatchQuery['doctorInfo.name'] = { $regex: doctorName, $options: 'i' };
      }
      pipeline.push({ $match: nameMatchQuery });
    }

    // Count total for pagination
    const countPipeline = [...pipeline, { $count: 'total' }];
    const countResult = await Consultation.aggregate(countPipeline);
    const total = countResult.length > 0 ? countResult[0].total : 0;

    // Add pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    pipeline.push(
      { $sort: { appointmentDate: -1 } },
      { $skip: skip },
      { $limit: limitNum }
    );

    const consultations = await Consultation.aggregate(pipeline);

    res.status(200).json({
      success: true,
      count: consultations.length,
      total,
      pagination: {
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum),
      },
      data: consultations,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  globalSearch,
  advancedDoctorSearch,
  advancedPatientSearch,
  advancedConsultationSearch,
};
