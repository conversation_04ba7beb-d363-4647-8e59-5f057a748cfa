const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const Consultation = require('../models/Consultation');

// @desc    Bulk import doctors
// @route   POST /api/bulk/doctors
// @access  Private (Admin only)
const bulkImportDoctors = async (req, res, next) => {
  try {
    const { doctors } = req.body;

    if (!Array.isArray(doctors) || doctors.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of doctors',
      });
    }

    // Validate required fields for each doctor
    const requiredFields = ['name', 'email', 'phone', 'specialization', 'experience', 'consultationFee'];
    const errors = [];

    doctors.forEach((doctor, index) => {
      requiredFields.forEach(field => {
        if (!doctor[field]) {
          errors.push(`Doctor ${index + 1}: ${field} is required`);
        }
      });
    });

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors,
      });
    }

    // Check for duplicate emails and registration numbers
    const emails = doctors.map(d => d.email);
    const regNumbers = doctors.map(d => d.registrationNumber);

    const existingDoctors = await Doctor.find({
      $or: [
        { email: { $in: emails } },
        { registrationNumber: { $in: regNumbers } }
      ]
    });

    if (existingDoctors.length > 0) {
      const duplicateEmails = existingDoctors.filter(d => emails.includes(d.email)).map(d => d.email);
      const duplicateRegNumbers = existingDoctors.filter(d => regNumbers.includes(d.registrationNumber)).map(d => d.registrationNumber);
      
      return res.status(400).json({
        success: false,
        message: 'Duplicate entries found',
        duplicateEmails,
        duplicateRegNumbers,
      });
    }

    // Insert doctors
    const createdDoctors = await Doctor.insertMany(doctors);

    res.status(201).json({
      success: true,
      message: `Successfully imported ${createdDoctors.length} doctors`,
      data: createdDoctors,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Bulk import patients
// @route   POST /api/bulk/patients
// @access  Private (Admin only)
const bulkImportPatients = async (req, res, next) => {
  try {
    const { patients } = req.body;

    if (!Array.isArray(patients) || patients.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of patients',
      });
    }

    // Validate required fields for each patient
    const requiredFields = ['name', 'email', 'phone', 'dateOfBirth', 'gender'];
    const errors = [];

    patients.forEach((patient, index) => {
      requiredFields.forEach(field => {
        if (!patient[field]) {
          errors.push(`Patient ${index + 1}: ${field} is required`);
        }
      });
    });

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors,
      });
    }

    // Check for duplicate emails
    const emails = patients.map(p => p.email);
    const existingPatients = await Patient.find({ email: { $in: emails } });

    if (existingPatients.length > 0) {
      const duplicateEmails = existingPatients.map(p => p.email);
      
      return res.status(400).json({
        success: false,
        message: 'Duplicate emails found',
        duplicateEmails,
      });
    }

    // Insert patients
    const createdPatients = await Patient.insertMany(patients);

    res.status(201).json({
      success: true,
      message: `Successfully imported ${createdPatients.length} patients`,
      data: createdPatients,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Bulk update consultation status
// @route   PUT /api/bulk/consultations/status
// @access  Private
const bulkUpdateConsultationStatus = async (req, res, next) => {
  try {
    const { consultationIds, status } = req.body;

    if (!Array.isArray(consultationIds) || consultationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of consultation IDs',
      });
    }

    if (!status || !['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid status',
      });
    }

    const result = await Consultation.updateMany(
      { _id: { $in: consultationIds } },
      { status }
    );

    res.status(200).json({
      success: true,
      message: `Successfully updated ${result.modifiedCount} consultations`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Bulk delete consultations
// @route   DELETE /api/bulk/consultations
// @access  Private (Admin only)
const bulkDeleteConsultations = async (req, res, next) => {
  try {
    const { consultationIds } = req.body;

    if (!Array.isArray(consultationIds) || consultationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of consultation IDs',
      });
    }

    // Check if any consultations are in progress
    const inProgressConsultations = await Consultation.find({
      _id: { $in: consultationIds },
      status: 'In Progress',
    });

    if (inProgressConsultations.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete consultations that are in progress',
        inProgressIds: inProgressConsultations.map(c => c._id),
      });
    }

    const result = await Consultation.deleteMany({
      _id: { $in: consultationIds }
    });

    res.status(200).json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} consultations`,
      deletedCount: result.deletedCount,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Export data
// @route   GET /api/bulk/export/:type
// @access  Private (Admin only)
const exportData = async (req, res, next) => {
  try {
    const { type } = req.params;
    const { format = 'json' } = req.query;

    let data;
    let filename;

    switch (type) {
      case 'doctors':
        data = await Doctor.find({}).select('-__v');
        filename = `doctors_export_${new Date().toISOString().split('T')[0]}`;
        break;
      case 'patients':
        data = await Patient.find({}).select('-__v');
        filename = `patients_export_${new Date().toISOString().split('T')[0]}`;
        break;
      case 'consultations':
        data = await Consultation.find({})
          .populate('patient', 'name email')
          .populate('doctor', 'name specialization')
          .select('-__v');
        filename = `consultations_export_${new Date().toISOString().split('T')[0]}`;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid export type. Use: doctors, patients, or consultations',
        });
    }

    if (format === 'csv') {
      // Convert to CSV format
      if (data.length === 0) {
        return res.status(200).json({
          success: true,
          message: 'No data to export',
          data: [],
        });
      }

      const headers = Object.keys(data[0].toObject ? data[0].toObject() : data[0]);
      const csvRows = [headers.join(',')];

      data.forEach(item => {
        const values = headers.map(header => {
          const value = item[header];
          return typeof value === 'object' ? JSON.stringify(value) : value;
        });
        csvRows.push(values.join(','));
      });

      const csvContent = csvRows.join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.send(csvContent);
    } else {
      // JSON format
      res.status(200).json({
        success: true,
        count: data.length,
        filename: `${filename}.json`,
        data,
      });
    }
  } catch (error) {
    next(error);
  }
};

module.exports = {
  bulkImportDoctors,
  bulkImportPatients,
  bulkUpdateConsultationStatus,
  bulkDeleteConsultations,
  exportData,
};
