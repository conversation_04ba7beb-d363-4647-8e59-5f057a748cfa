const express = require('express');
const {
  getSettings,
  updateSettings,
} = require('../controllers/settingsController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication and admin role
router.use(protect);
router.use(authorize('admin'));

// Routes
router.route('/')
  .get(getSettings)
  .put(updateSettings);

module.exports = router;
