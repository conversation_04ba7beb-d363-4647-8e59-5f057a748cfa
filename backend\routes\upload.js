const express = require('express');
const multer = require('multer');
const {
  uploadFile,
  getFile,
  deleteFile,
  uploadAvatar,
  getAvatar,
  getUploadStats,
} = require('../controllers/uploadController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Configure multer for memory storage
const storage = multer.memoryStorage();

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow all file types, validation is done in controller
    cb(null, true);
  },
});

// All routes require authentication
router.use(protect);

// Routes
router.post('/', upload.single('file'), uploadFile);
router.post('/avatar', upload.single('avatar'), uploadAvatar);
router.get('/stats', authorize('admin'), getUploadStats);
router.get('/files/:filename', getFile);
router.get('/avatars/:filename', getAvatar);
router.delete('/files/:filename', deleteFile);

module.exports = router;
