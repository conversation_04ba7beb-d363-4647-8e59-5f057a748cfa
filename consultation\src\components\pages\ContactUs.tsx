import { useState } from "react";
import { Card } from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { toast } from "sonner@2.0.3";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  MessageCircle, 
  Send,
  Headphones,
  FileQuestion,
  CreditCard
} from "lucide-react";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

interface ContactUsProps {
  onNavigate: (page: PageType) => void;
}

interface ContactForm {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

const contactMethods = [
  {
    icon: Mail,
    title: "Email Support",
    content: "<EMAIL>",
    description: "Get help via email within 24 hours"
  },
  {
    icon: Phone,
    title: "Phone Support",
    content: "+91 1800-AYURA-1",
    description: "Monday to Friday, 9 AM - 6 PM IST"
  },
  {
    icon: MessageCircle,
    title: "Live Chat",
    content: "Available Now",
    description: "Instant support for quick questions"
  }
];

const faqCategories = [
  {
    icon: FileQuestion,
    title: "General Questions",
    description: "How Ayura works, booking process, consultations"
  },
  {
    icon: Headphones,
    title: "Technical Support",
    description: "Video issues, app problems, account help"
  },
  {
    icon: CreditCard,
    title: "Billing & Payments",
    description: "Payment methods, refunds, invoice queries"
  }
];

export function ContactUs({ onNavigate }: ContactUsProps) {
  const [formData, setFormData] = useState<ContactForm>({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  });

  const handleInputChange = (field: keyof ContactForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Simulate form submission
    toast.success("Your message has been sent! We'll respond within 24 hours.");
    
    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: ""
    });
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="px-6 py-20 bg-gradient-to-br from-green-50 to-amber-50">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">Get in Touch</h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            Have questions about Ayura? Need support with your consultation? 
            We're here to help you on your wellness journey.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="px-6 py-20 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {contactMethods.map((method, index) => (
              <Card key={index} className="p-8 text-center hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <method.icon className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{method.title}</h3>
                <p className="text-lg font-medium text-green-600 mb-2">{method.content}</p>
                <p className="text-sm text-gray-600">{method.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="px-6 py-20 bg-gray-50">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card className="p-8 bg-white shadow-lg">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                        Full Name *
                      </Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Your full name"
                        className="mt-1"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                        Phone Number
                      </Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+91 98765 43210"
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                      Email Address *
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="mt-1"
                      required
                    />
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      Subject *
                    </Label>
                    <Select value={formData.subject} onValueChange={(value) => handleInputChange('subject', value)}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Choose a subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General Inquiry</SelectItem>
                        <SelectItem value="booking">Booking Support</SelectItem>
                        <SelectItem value="technical">Technical Issue</SelectItem>
                        <SelectItem value="billing">Billing Question</SelectItem>
                        <SelectItem value="doctor">Doctor Inquiry</SelectItem>
                        <SelectItem value="partnership">Partnership Opportunity</SelectItem>
                        <SelectItem value="feedback">Feedback</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="message" className="text-sm font-medium text-gray-700">
                      Message *
                    </Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      placeholder="Please describe your question or concern in detail..."
                      className="mt-1 min-h-[120px]"
                      required
                    />
                  </div>

                  <Button 
                    type="submit"
                    size="lg"
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    <Send className="w-5 h-5 mr-2" />
                    Send Message
                  </Button>
                </form>
              </Card>
            </div>

            {/* Contact Info & FAQ */}
            <div className="space-y-8">
              {/* Office Info */}
              <Card className="p-6 bg-white shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Office Information</h3>
                
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-5 h-5 text-green-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900">Headquarters</p>
                      <p className="text-sm text-gray-600">
                        123 Wellness Street<br />
                        Health District, Mumbai<br />
                        Maharashtra 400001, India
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Clock className="w-5 h-5 text-green-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900">Business Hours</p>
                      <p className="text-sm text-gray-600">
                        Monday - Friday: 9:00 AM - 6:00 PM IST<br />
                        Saturday: 10:00 AM - 4:00 PM IST<br />
                        Sunday: Closed
                      </p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* FAQ Categories */}
              <Card className="p-6 bg-white shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Help</h3>
                
                <div className="space-y-3">
                  {faqCategories.map((category, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                      <category.icon className="w-5 h-5 text-green-600 mt-1" />
                      <div>
                        <p className="font-medium text-gray-900 text-sm">{category.title}</p>
                        <p className="text-xs text-gray-600">{category.description}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <Button variant="outline" className="w-full mt-4">
                  View All FAQs
                </Button>
              </Card>

              {/* Emergency Contact */}
              <Card className="p-6 bg-red-50 border-red-200 shadow-lg">
                <h3 className="text-lg font-semibold text-red-900 mb-2">Emergency?</h3>
                <p className="text-sm text-red-700 mb-3">
                  For medical emergencies, please contact your local emergency services immediately.
                </p>
                <p className="text-sm text-red-600">
                  Emergency Hotline: <span className="font-semibold">108</span>
                </p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Live Chat CTA */}
      <section className="px-6 py-16 bg-green-600">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Need Immediate Help?</h2>
          <p className="text-xl text-green-100 mb-8">
            Start a live chat with our support team for instant assistance
          </p>
          <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
            <MessageCircle className="w-5 h-5 mr-2" />
            Start Live Chat
          </Button>
        </div>
      </section>
    </div>
  );
}