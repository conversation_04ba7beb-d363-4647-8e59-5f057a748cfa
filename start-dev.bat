@echo off
echo 🚀 Starting Ayura Admin Panel Development Environment...
echo.

echo 📦 Checking backend dependencies...
if not exist "backend\node_modules" (
    echo Installing backend dependencies...
    cd backend
    call npm install
    cd ..
    echo ✅ Backend dependencies installed!
    echo.
)

echo 🎯 Starting both Frontend and Backend servers...
echo.
echo Frontend will be available at: http://localhost:3000
echo Backend will be available at: http://localhost:5000
echo.
echo Press Ctrl+C to stop both servers
echo.

npx concurrently --names "FRONTEND,BACKEND" --prefix-colors "cyan,yellow" "npm run dev:frontend" "npm run dev:backend"
