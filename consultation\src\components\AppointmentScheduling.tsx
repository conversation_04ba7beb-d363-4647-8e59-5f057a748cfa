import { Card } from "./ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Button } from "./ui/button";
import { Label } from "./ui/label";
import { Calendar, Clock, User } from "lucide-react";

interface Doctor {
  id: string;
  name: string;
  specialization: string;
  experience: number;
  rating: number;
  languages: string[];
}

const doctors: Doctor[] = [
  {
    id: "dr-sharma",
    name: "Dr. <PERSON><PERSON>",
    specialization: "Panchakarma & Detox",
    experience: 15,
    rating: 4.8,
    languages: ["Hindi", "English"]
  },
  {
    id: "dr-k<PERSON><PERSON><PERSON>",
    name: "Dr<PERSON>",
    specialization: "Chronic Diseases",
    experience: 20,
    rating: 4.9,
    languages: ["Tamil", "English", "Hindi"]
  },
  {
    id: "dr-patel",
    name: "Dr. <PERSON><PERSON>",
    specialization: "Women's Health",
    experience: 12,
    rating: 4.7,
    languages: ["Gujarati", "English"]
  }
];

const timeSlots = [
  "09:00 AM", "10:00 AM", "11:00 AM", "12:00 PM",
  "02:00 PM", "03:00 PM", "04:00 PM", "05:00 PM",
  "06:00 PM", "07:00 PM"
];

const nextDays = Array.from({ length: 7 }, (_, i) => {
  const date = new Date();
  date.setDate(date.getDate() + i);
  return {
    value: date.toISOString().split('T')[0],
    label: i === 0 ? 'Today' : i === 1 ? 'Tomorrow' : date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    })
  };
});

interface AppointmentSchedulingProps {
  selectedDoctor: string | null;
  selectedDate: string | null;
  selectedTime: string | null;
  onDoctorSelect: (doctorId: string) => void;
  onDateSelect: (date: string) => void;
  onTimeSelect: (time: string) => void;
}

export function AppointmentScheduling({
  selectedDoctor,
  selectedDate,
  selectedTime,
  onDoctorSelect,
  onDateSelect,
  onTimeSelect
}: AppointmentSchedulingProps) {
  const getSelectedDoctor = () => doctors.find(d => d.id === selectedDoctor);

  return (
    <Card className="p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <User className="w-5 h-5 mr-2" />
        Doctor Selection & Scheduling
      </h2>

      <div className="space-y-6">
        {/* Doctor Selection */}
        <div>
          <Label className="text-sm font-medium text-gray-700 mb-2 block">
            Choose Your Doctor
          </Label>
          <Select value={selectedDoctor || ""} onValueChange={onDoctorSelect}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a doctor" />
            </SelectTrigger>
            <SelectContent>
              {doctors.map((doctor) => (
                <SelectItem key={doctor.id} value={doctor.id}>
                  <div className="flex flex-col">
                    <span className="font-medium">{doctor.name}</span>
                    <span className="text-sm text-gray-500">{doctor.specialization}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {selectedDoctor && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">{getSelectedDoctor()?.name}</span>
                <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                  ⭐ {getSelectedDoctor()?.rating}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-1">{getSelectedDoctor()?.specialization}</p>
              <p className="text-sm text-gray-500">
                {getSelectedDoctor()?.experience} years experience • 
                Languages: {getSelectedDoctor()?.languages.join(', ')}
              </p>
            </div>
          )}
        </div>

        {/* Date Selection */}
        <div>
          <Label className="text-sm font-medium text-gray-700 mb-2 block flex items-center">
            <Calendar className="w-4 h-4 mr-1" />
            Select Date
          </Label>
          <Select value={selectedDate || ""} onValueChange={onDateSelect}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose date" />
            </SelectTrigger>
            <SelectContent>
              {nextDays.map((day) => (
                <SelectItem key={day.value} value={day.value}>
                  {day.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Time Selection */}
        <div>
          <Label className="text-sm font-medium text-gray-700 mb-3 block flex items-center">
            <Clock className="w-4 h-4 mr-1" />
            Available Time Slots
          </Label>
          <div className="grid grid-cols-2 gap-2">
            {timeSlots.map((time) => (
              <Button
                key={time}
                variant={selectedTime === time ? "default" : "outline"}
                size="sm"
                onClick={() => onTimeSelect(time)}
                className={selectedTime === time ? "bg-green-600 hover:bg-green-700" : ""}
              >
                {time}
              </Button>
            ))}
          </div>
        </div>

        {/* Summary */}
        {selectedDoctor && selectedDate && selectedTime && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-medium text-green-900 mb-2">Appointment Summary</h3>
            <div className="text-sm text-green-800 space-y-1">
              <p>Doctor: {getSelectedDoctor()?.name}</p>
              <p>Date: {nextDays.find(d => d.value === selectedDate)?.label}</p>
              <p>Time: {selectedTime}</p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}