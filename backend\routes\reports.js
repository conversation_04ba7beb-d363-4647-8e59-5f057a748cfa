const express = require('express');
const { query } = require('express-validator');
const {
  getConsultationReport,
  getDoctorReport,
  getRevenueReport,
} = require('../controllers/reportsController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication and admin role
router.use(protect);
router.use(authorize('admin'));

// Validation rules for date queries
const dateValidation = [
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid date'),
  query('format').optional().isIn(['json', 'csv']).withMessage('Format must be json or csv'),
];

// Routes
router.get('/consultations', dateValidation, getConsultationReport);
router.get('/doctors', dateValidation, getDoctorReport);
router.get('/revenue', [
  ...dateValidation,
  query('groupBy').optional().isIn(['day', 'week', 'month', 'year']).withMessage('GroupBy must be day, week, month, or year'),
], getRevenueReport);

module.exports = router;
