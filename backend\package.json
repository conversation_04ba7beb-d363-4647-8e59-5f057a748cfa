{"name": "ayura-admin-backend", "version": "1.0.0", "description": "Backend server for Ayura Admin Panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node utils/seedData.js", "test": "node test-apis.js", "test:edit-delete": "node test-edit-delete.js", "test:pending": "node test-pending-apis.js", "test:unit": "echo \"Error: no unit tests specified\" && exit 1"}, "keywords": ["ayura", "admin", "backend", "express", "nodejs"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.4", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^2.0.2"}, "devDependencies": {"nodemon": "^3.0.2"}}