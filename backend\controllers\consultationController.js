const { validationResult } = require('express-validator');
const Consultation = require('../models/Consultation');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');

// Helper function to track changes
const getChangedFields = (original, updated) => {
  const changes = {};
  const excludeFields = ['_id', '__v', 'createdAt', 'updatedAt'];

  for (const key in updated) {
    if (!excludeFields.includes(key) && JSON.stringify(original[key]) !== JSON.stringify(updated[key])) {
      changes[key] = {
        from: original[key],
        to: updated[key],
      };
    }
  }

  return changes;
};

// @desc    Get all consultations
// @route   GET /api/consultations
// @access  Private
const getConsultations = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build query
    let query = {};
    
    // Filter by status
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query.appointmentDate = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    }

    // Filter by doctor
    if (req.query.doctor) {
      query.doctor = req.query.doctor;
    }

    // Filter by patient
    if (req.query.patient) {
      query.patient = req.query.patient;
    }

    const consultations = await Consultation.find(query)
      .populate('patient', 'name email phone')
      .populate('doctor', 'name specialization')
      .sort({ appointmentDate: -1 })
      .limit(limit)
      .skip(startIndex);

    const total = await Consultation.countDocuments(query);

    res.status(200).json({
      success: true,
      count: consultations.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
      data: consultations,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single consultation
// @route   GET /api/consultations/:id
// @access  Private
const getConsultation = async (req, res, next) => {
  try {
    const consultation = await Consultation.findById(req.params.id)
      .populate('patient')
      .populate('doctor');

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found',
      });
    }

    res.status(200).json({
      success: true,
      data: consultation,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new consultation
// @route   POST /api/consultations
// @access  Private
const createConsultation = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    // Verify patient exists
    const patient = await Patient.findById(req.body.patient);
    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
      });
    }

    // Verify doctor exists
    const doctor = await Doctor.findById(req.body.doctor);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    // Check for scheduling conflicts
    const conflictingConsultation = await Consultation.findOne({
      doctor: req.body.doctor,
      appointmentDate: req.body.appointmentDate,
      appointmentTime: req.body.appointmentTime,
      status: { $in: ['Scheduled', 'In Progress'] },
    });

    if (conflictingConsultation) {
      return res.status(400).json({
        success: false,
        message: 'Doctor is not available at this time',
      });
    }

    const consultation = await Consultation.create(req.body);

    // Update patient and doctor consultation counts
    await Patient.findByIdAndUpdate(req.body.patient, {
      $inc: { totalConsultations: 1 },
      lastVisit: new Date(),
    });

    await Doctor.findByIdAndUpdate(req.body.doctor, {
      $inc: { totalConsultations: 1 },
    });

    const populatedConsultation = await Consultation.findById(consultation._id)
      .populate('patient', 'name email phone')
      .populate('doctor', 'name specialization');

    res.status(201).json({
      success: true,
      data: populatedConsultation,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update consultation
// @route   PUT /api/consultations/:id
// @access  Private
const updateConsultation = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array(),
      });
    }

    let consultation = await Consultation.findById(req.params.id)
      .populate('patient', 'name email phone')
      .populate('doctor', 'name specialization');

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found',
      });
    }

    // Store original data for comparison
    const originalData = consultation.toObject();

    // Validate status transitions
    const validStatusTransitions = {
      'Scheduled': ['In Progress', 'Cancelled', 'No Show'],
      'In Progress': ['Completed', 'Cancelled'],
      'Completed': [], // Cannot change from completed
      'Cancelled': ['Scheduled'], // Can reschedule
      'No Show': ['Scheduled'], // Can reschedule
    };

    if (req.body.status && req.body.status !== consultation.status) {
      const allowedTransitions = validStatusTransitions[consultation.status] || [];
      if (!allowedTransitions.includes(req.body.status)) {
        return res.status(400).json({
          success: false,
          message: `Cannot change status from '${consultation.status}' to '${req.body.status}'`,
          allowedTransitions,
        });
      }
    }

    // Validate appointment date/time changes
    if ((req.body.appointmentDate || req.body.appointmentTime) && consultation.status === 'In Progress') {
      return res.status(400).json({
        success: false,
        message: 'Cannot change appointment date/time for consultation in progress',
      });
    }

    // Check doctor availability if changing doctor or appointment time
    if (req.body.doctor && req.body.doctor !== consultation.doctor._id.toString()) {
      const newDoctor = await Doctor.findById(req.body.doctor);
      if (!newDoctor || !newDoctor.isActive) {
        return res.status(400).json({
          success: false,
          message: 'Selected doctor is not available',
        });
      }
    }

    // Validate fee changes
    if (req.body.fee && consultation.status === 'Completed') {
      return res.status(400).json({
        success: false,
        message: 'Cannot change fee for completed consultation',
      });
    }

    // Update consultation with validation
    consultation = await Consultation.findByIdAndUpdate(req.params.id, {
      ...req.body,
      updatedAt: new Date(),
    }, {
      new: true,
      runValidators: true,
    }).populate('patient', 'name email phone').populate('doctor', 'name specialization');

    // Log the update for audit trail
    console.log(`Consultation updated: ${consultation._id} by user ${req.user.id}`);

    // Send notifications for critical changes
    const criticalChanges = ['status', 'appointmentDate', 'appointmentTime', 'doctor'];
    const hasCriticalChanges = criticalChanges.some(field =>
      req.body[field] && JSON.stringify(req.body[field]) !== JSON.stringify(originalData[field])
    );

    if (hasCriticalChanges) {
      console.log(`Critical consultation changes detected for consultation ${consultation._id}`);
      // Here you could add email/SMS notification logic
    }

    res.status(200).json({
      success: true,
      message: 'Consultation updated successfully',
      data: consultation,
      changes: getChangedFields(originalData, consultation.toObject()),
      hasCriticalChanges,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete consultation
// @route   DELETE /api/consultations/:id
// @access  Private (Admin only)
const deleteConsultation = async (req, res, next) => {
  try {
    const { force = false } = req.query; // Force delete parameter
    const consultation = await Consultation.findById(req.params.id)
      .populate('patient', 'name email')
      .populate('doctor', 'name specialization');

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found',
      });
    }

    // Check consultation status and restrictions
    const restrictedStatuses = ['In Progress'];
    if (restrictedStatuses.includes(consultation.status) && !force) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete consultation with status '${consultation.status}'`,
        suggestion: 'Complete or cancel the consultation first, or use ?force=true to force delete.',
      });
    }

    // Check if consultation is completed and has important data
    const hasImportantData = consultation.diagnosis?.primary ||
                            consultation.treatment?.medications?.length > 0 ||
                            consultation.vitals?.bloodPressure ||
                            consultation.notes;

    if (consultation.status === 'Completed' && hasImportantData && !force) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete completed consultation with medical data',
        data: {
          hasDiagnosis: !!consultation.diagnosis?.primary,
          hasTreatment: !!consultation.treatment?.medications?.length,
          hasVitals: !!consultation.vitals?.bloodPressure,
          hasNotes: !!consultation.notes,
        },
        suggestion: 'Use ?force=true to force delete and permanently remove medical data.',
      });
    }

    // Warn about upcoming consultation deletion
    if (consultation.status === 'Scheduled' && new Date(consultation.appointmentDate) > new Date()) {
      console.log(`Deleting upcoming consultation: ${consultation._id} scheduled for ${consultation.appointmentDate}`);
    }

    // Store consultation data for response
    const deletedConsultationData = {
      id: consultation._id,
      patient: consultation.patient.name,
      doctor: consultation.doctor.name,
      appointmentDate: consultation.appointmentDate,
      appointmentTime: consultation.appointmentTime,
      status: consultation.status,
      type: consultation.type,
    };

    // Permanently delete consultation
    await Consultation.findByIdAndDelete(req.params.id);

    // Update doctor and patient consultation counts
    await Doctor.findByIdAndUpdate(consultation.doctor._id, {
      $inc: { totalConsultations: -1 }
    });

    await Patient.findByIdAndUpdate(consultation.patient._id, {
      $inc: { totalConsultations: -1 }
    });

    console.log(`Consultation deleted: ${consultation._id} by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Consultation deleted successfully',
      data: {
        deletedConsultation: deletedConsultationData,
        hadMedicalData: hasImportantData,
        wasForced: force,
      },
      warning: hasImportantData ? 'Medical data has been permanently removed' : undefined,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update consultation status
// @route   PUT /api/consultations/:id/status
// @access  Private
const updateConsultationStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const consultation = await Consultation.findByIdAndUpdate(
      req.params.id,
      { status: req.body.status },
      { new: true, runValidators: true }
    ).populate('patient', 'name email phone').populate('doctor', 'name specialization');

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found',
      });
    }

    res.status(200).json({
      success: true,
      data: consultation,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get consultations by doctor
// @route   GET /api/consultations/doctor/:doctorId
// @access  Private
const getConsultationsByDoctor = async (req, res, next) => {
  try {
    const consultations = await Consultation.find({ doctor: req.params.doctorId })
      .populate('patient', 'name email phone')
      .sort({ appointmentDate: -1 });

    res.status(200).json({
      success: true,
      count: consultations.length,
      data: consultations,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get consultations by patient
// @route   GET /api/consultations/patient/:patientId
// @access  Private
const getConsultationsByPatient = async (req, res, next) => {
  try {
    const consultations = await Consultation.find({ patient: req.params.patientId })
      .populate('doctor', 'name specialization')
      .sort({ appointmentDate: -1 });

    res.status(200).json({
      success: true,
      count: consultations.length,
      data: consultations,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getConsultations,
  getConsultation,
  createConsultation,
  updateConsultation,
  deleteConsultation,
  updateConsultationStatus,
  getConsultationsByDoctor,
  getConsultationsByPatient,
};
