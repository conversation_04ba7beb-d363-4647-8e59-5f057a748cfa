const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  platform: {
    platformName: {
      type: String,
      default: 'Ayura - Ayurvedic Consultations',
    },
    supportEmail: {
      type: String,
      default: '<EMAIL>',
    },
    supportPhone: {
      type: String,
      default: '+91 98765 43210',
    },
    timezone: {
      type: String,
      default: 'Asia/Kolkata',
    },
    defaultLanguage: {
      type: String,
      default: 'en',
    },
    maintenanceMode: {
      type: Boolean,
      default: false,
    },
    registrationEnabled: {
      type: Boolean,
      default: true,
    },
    maxDailyConsultations: {
      type: Number,
      default: 50,
    },
    consultationBuffer: {
      type: Number,
      default: 15, // minutes
    },
  },
  notifications: {
    emailNotifications: {
      type: <PERSON>olean,
      default: true,
    },
    smsNotifications: {
      type: Boolean,
      default: true,
    },
    pushNotifications: {
      type: Boolean,
      default: false,
    },
    appointmentReminders: {
      type: Boolean,
      default: true,
    },
    paymentAlerts: {
      type: Boolean,
      default: true,
    },
    doctorAlerts: {
      type: <PERSON>olean,
      default: true,
    },
    systemAlerts: {
      type: <PERSON>olean,
      default: true,
    },
    reminderTime: {
      type: Number,
      default: 24, // hours
    },
  },
  security: {
    passwordMinLength: {
      type: Number,
      default: 8,
    },
    requireSpecialChars: {
      type: Boolean,
      default: true,
    },
    sessionTimeout: {
      type: Number,
      default: 30, // minutes
    },
    maxLoginAttempts: {
      type: Number,
      default: 5,
    },
    twoFactorAuth: {
      type: Boolean,
      default: false,
    },
    dataRetentionDays: {
      type: Number,
      default: 2555, // 7 years
    },
    encryptSensitiveData: {
      type: Boolean,
      default: true,
    },
  },
  payments: {
    currency: {
      type: String,
      default: 'INR',
    },
    paymentMethods: [{
      type: String,
      enum: ['razorpay', 'stripe', 'paytm', 'cash', 'card', 'upi'],
    }],
    autoRefund: {
      type: Boolean,
      default: false,
    },
    refundPeriod: {
      type: Number,
      default: 7, // days
    },
    taxRate: {
      type: Number,
      default: 18, // percentage
    },
    processingFee: {
      type: Number,
      default: 2.5, // percentage
    },
  },
  branding: {
    primaryColor: {
      type: String,
      default: '#93a580',
    },
    secondaryColor: {
      type: String,
      default: '#7a9167',
    },
    logoUrl: {
      type: String,
      default: '',
    },
    faviconUrl: {
      type: String,
      default: '',
    },
    customCSS: {
      type: String,
      default: '',
    },
    footerText: {
      type: String,
      default: '© 2024 Ayura. All rights reserved.',
    },
  },
  integrations: {
    googleCalendar: {
      type: Boolean,
      default: false,
    },
    zoomIntegration: {
      type: Boolean,
      default: true,
    },
    whatsappApi: {
      type: Boolean,
      default: false,
    },
    emailProvider: {
      type: String,
      enum: ['sendgrid', 'mailgun', 'ses', 'smtp'],
      default: 'sendgrid',
    },
    smsProvider: {
      type: String,
      enum: ['twilio', 'msg91', 'textlocal'],
      default: 'twilio',
    },
    paymentGateway: {
      type: String,
      enum: ['razorpay', 'stripe', 'paytm'],
      default: 'razorpay',
    },
  },
}, {
  timestamps: true,
});

// Ensure only one settings document exists
settingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

module.exports = mongoose.model('Settings', settingsSchema);
