import { useState } from "react";
import { Card } from "../ui/card";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Badge } from "../ui/badge";
import { ImageWithFallback } from "../figma/ImageWithFallback";
import { Search, Star, Clock, Languages, Award } from "lucide-react";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

interface Doctor {
  id: string;
  name: string;
  image: string;
  specialization: string;
  experience: number;
  rating: number;
  reviewCount: number;
  consultationFee: number;
  languages: string[];
  availableToday: boolean;
  bio: string;
  qualifications: string[];
}

const doctors: Doctor[] = [
  {
    id: "dr-sharma",
    name: "Dr. <PERSON><PERSON>",
    image: "https://images.unsplash.com/photo-**********-2b71ea197ec2?auto=format&fit=crop&w=400&q=80",
    specialization: "Digestive Health & Panchakarma",
    experience: 15,
    rating: 4.8,
    reviewCount: 127,
    consultationFee: 2000,
    languages: ["Hindi", "English"],
    availableToday: true,
    bio: "Specializes in digestive disorders and detoxification therapies with focus on traditional Panchakarma treatments.",
    qualifications: ["BAMS", "MD Ayurveda", "Panchakarma Specialist"]
  },
  {
    id: "dr-krishnan",
    name: "Dr. <PERSON>n",
    image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?auto=format&fit=crop&w=400&q=80",
    specialization: "Chronic Diseases & Pain Management",
    experience: 20,
    rating: 4.9,
    reviewCount: 203,
    consultationFee: 2500,
    languages: ["Tamil", "English", "Hindi"],
    availableToday: true,
    bio: "Expert in managing chronic conditions like diabetes, arthritis, and autoimmune disorders through Ayurvedic principles.",
    qualifications: ["BAMS", "MD Ayurveda", "PhD Research"]
  },
  {
    id: "dr-patel",
    name: "Dr. Meera Patel",
    image: "https://images.unsplash.com/photo-1594824087757-56fc670ad2b4?auto=format&fit=crop&w=400&q=80",
    specialization: "Women's Health & Wellness",
    experience: 12,
    rating: 4.7,
    reviewCount: 89,
    consultationFee: 1800,
    languages: ["Gujarati", "English"],
    availableToday: false,
    bio: "Focuses on women's reproductive health, PCOS, menstrual disorders, and prenatal care through natural healing.",
    qualifications: ["BAMS", "Women's Health Specialist"]
  },
  {
    id: "dr-reddy",
    name: "Dr. Arjun Reddy",
    image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?auto=format&fit=crop&w=400&q=80",
    specialization: "Stress Management & Mental Wellness",
    experience: 10,
    rating: 4.6,
    reviewCount: 156,
    consultationFee: 1500,
    languages: ["Telugu", "English", "Hindi"],
    availableToday: true,
    bio: "Specializes in anxiety, depression, and stress-related disorders using meditation and herbal therapies.",
    qualifications: ["BAMS", "Yoga Therapy Certificate"]
  },
  {
    id: "dr-nair",
    name: "Dr. Lakshmi Nair",
    image: "https://images.unsplash.com/photo-1651008376811-b98baee60c1f?auto=format&fit=crop&w=400&q=80",
    specialization: "Skin & Hair Care",
    experience: 8,
    rating: 4.5,
    reviewCount: 78,
    consultationFee: 1200,
    languages: ["Malayalam", "English"],
    availableToday: true,
    bio: "Expert in treating skin conditions like eczema, psoriasis, acne, and hair loss through natural remedies.",
    qualifications: ["BAMS", "Dermatology Specialist"]
  },
  {
    id: "dr-gupta",
    name: "Dr. Vikram Gupta",
    image: "https://images.unsplash.com/photo-1622253692010-333f2da6031d?auto=format&fit=crop&w=400&q=80",
    specialization: "General Wellness & Preventive Care",
    experience: 18,
    rating: 4.8,
    reviewCount: 192,
    consultationFee: 1800,
    languages: ["Hindi", "English", "Punjabi"],
    availableToday: true,
    bio: "Focuses on preventive healthcare, immunity building, and overall wellness through lifestyle modifications.",
    qualifications: ["BAMS", "MD Ayurveda", "Wellness Coach"]
  }
];

const specializations = [
  "All Specializations",
  "Digestive Health & Panchakarma",
  "Chronic Diseases & Pain Management", 
  "Women's Health & Wellness",
  "Stress Management & Mental Wellness",
  "Skin & Hair Care",
  "General Wellness & Preventive Care"
];

interface ConsultationsListProps {
  onNavigate: (page: PageType) => void;
}

export function ConsultationsList({ onNavigate }: ConsultationsListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSpecialization, setSelectedSpecialization] = useState("All Specializations");
  const [sortBy, setSortBy] = useState("rating");
  const [showAvailableOnly, setShowAvailableOnly] = useState(false);

  const filteredDoctors = doctors
    .filter(doctor => {
      const matchesSearch = doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           doctor.specialization.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesSpecialization = selectedSpecialization === "All Specializations" || 
                                   doctor.specialization === selectedSpecialization;
      const matchesAvailability = !showAvailableOnly || doctor.availableToday;
      
      return matchesSearch && matchesSpecialization && matchesAvailability;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "rating":
          return b.rating - a.rating;
        case "experience":
          return b.experience - a.experience;
        case "fee-low":
          return a.consultationFee - b.consultationFee;
        case "fee-high":
          return b.consultationFee - a.consultationFee;
        default:
          return 0;
      }
    });

  return (
    <div className="min-h-screen px-6 py-8">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Find Your Ayurvedic Doctor</h1>
          <p className="text-xl text-gray-600">Choose from our certified practitioners based on your health needs</p>
        </div>

        {/* Filters */}
        <Card className="p-6 mb-8 bg-white shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search doctors or specializations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedSpecialization} onValueChange={setSelectedSpecialization}>
              <SelectTrigger>
                <SelectValue placeholder="Specialization" />
              </SelectTrigger>
              <SelectContent>
                {specializations.map(spec => (
                  <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="experience">Most Experienced</SelectItem>
                <SelectItem value="fee-low">Price: Low to High</SelectItem>
                <SelectItem value="fee-high">Price: High to Low</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="availableToday"
                checked={showAvailableOnly}
                onChange={(e) => setShowAvailableOnly(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="availableToday" className="text-sm font-medium text-gray-700">
                Available Today
              </label>
            </div>
          </div>

          <div className="text-sm text-gray-600">
            Showing {filteredDoctors.length} doctor{filteredDoctors.length !== 1 ? 's' : ''}
          </div>
        </Card>

        {/* Doctors Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredDoctors.map((doctor) => (
            <Card key={doctor.id} className="p-6 hover:shadow-xl transition-shadow bg-white">
              <div className="flex space-x-4">
                <div className="flex-shrink-0">
                  <ImageWithFallback
                    src={doctor.image}
                    alt={doctor.name}
                    className="w-24 h-24 rounded-full object-cover"
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{doctor.name}</h3>
                      <p className="text-green-600 font-medium">{doctor.specialization}</p>
                    </div>
                    {doctor.availableToday && (
                      <Badge className="bg-green-100 text-green-800">Available Today</Badge>
                    )}
                  </div>

                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">{doctor.bio}</p>

                  <div className="flex flex-wrap gap-2 mb-3">
                    {doctor.qualifications.map((qual, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {qual}
                      </Badge>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Award className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">{doctor.experience} years exp.</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{doctor.rating}</span>
                      <span className="text-gray-500">({doctor.reviewCount} reviews)</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">₹{doctor.consultationFee}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Languages className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">{doctor.languages.join(", ")}</span>
                    </div>
                  </div>

                  <Button 
                    onClick={() => onNavigate('book')}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    Book Consultation
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredDoctors.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No doctors found matching your criteria.</p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setSelectedSpecialization("All Specializations");
                setShowAvailableOnly(false);
              }}
              className="mt-4"
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}