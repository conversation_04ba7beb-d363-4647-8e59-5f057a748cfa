const express = require('express');
const {
  bulkImportDoctors,
  bulkImportPatients,
  bulkUpdateConsultationStatus,
  bulkDeleteConsultations,
  exportData,
} = require('../controllers/bulkController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Bulk import routes (Admin only)
router.post('/doctors', authorize('admin'), bulkImportDoctors);
router.post('/patients', authorize('admin'), bulkImportPatients);

// Bulk update routes
router.put('/consultations/status', bulkUpdateConsultationStatus);

// Bulk delete routes (Admin only)
router.delete('/consultations', authorize('admin'), bulkDeleteConsultations);

// Export routes (Admin only)
router.get('/export/:type', authorize('admin'), exportData);

module.exports = router;
