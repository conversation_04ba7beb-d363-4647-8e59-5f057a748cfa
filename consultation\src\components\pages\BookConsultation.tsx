import { useState } from "react";
import { ConsultationTypes } from "../ConsultationTypes";
import { AppointmentScheduling } from "../AppointmentScheduling";
import { PatientInformation } from "../PatientInformation";
import { Button } from "../ui/button";
import { toast } from "sonner@2.0.3";
import { CheckCircle, ArrowLeft } from "lucide-react";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

interface PatientInfo {
  fullName: string;
  email: string;
  phone: string;
  age: string;
  gender: string;
  medicalHistory: string;
  currentConcerns: string;
}

interface BookConsultationProps {
  onNavigate: (page: PageType) => void;
}

export function BookConsultation({ onNavigate }: BookConsultationProps) {
  const [selectedConsultationType, setSelectedConsultationType] = useState<string | null>(null);
  const [selectedDoctor, setSelectedDoctor] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [patientInfo, setPatientInfo] = useState<PatientInfo>({
    fullName: "",
    email: "",
    phone: "",
    age: "",
    gender: "",
    medicalHistory: "",
    currentConcerns: ""
  });

  const handlePatientInfoChange = (field: keyof PatientInfo, value: string) => {
    setPatientInfo(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const requiredFields = ['fullName', 'email', 'phone', 'age', 'gender', 'currentConcerns'];
    const missingFields = requiredFields.filter(field => !patientInfo[field as keyof PatientInfo]);
    
    if (!selectedConsultationType) {
      toast.error("Please select a consultation type");
      return false;
    }
    
    if (!selectedDoctor) {
      toast.error("Please select a doctor");
      return false;
    }
    
    if (!selectedDate) {
      toast.error("Please select a date");
      return false;
    }
    
    if (!selectedTime) {
      toast.error("Please select a time slot");
      return false;
    }
    
    if (missingFields.length > 0) {
      toast.error("Please fill in all required fields");
      return false;
    }
    
    return true;
  };

  const handleBookConsultation = () => {
    if (!validateForm()) return;
    
    // Simulate booking process
    toast.success("Consultation booked successfully! You will receive a confirmation email shortly.");
    
    // Reset form after successful booking
    setTimeout(() => {
      setSelectedConsultationType(null);
      setSelectedDoctor(null);
      setSelectedDate(null);
      setSelectedTime(null);
      setPatientInfo({
        fullName: "",
        email: "",
        phone: "",
        age: "",
        gender: "",
        medicalHistory: "",
        currentConcerns: ""
      });
    }, 2000);
  };

  const isFormComplete = selectedConsultationType && selectedDoctor && selectedDate && selectedTime && 
    patientInfo.fullName && patientInfo.email && patientInfo.phone && patientInfo.age && 
    patientInfo.gender && patientInfo.currentConcerns;

  return (
    <div className="min-h-screen px-6 py-8">
      <div className="mx-auto max-w-7xl">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => onNavigate('consultations')}
          className="mb-6 text-green-600 hover:text-green-700 hover:bg-green-50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Consultations
        </Button>

        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Book Your Ayurvedic Consultation</h1>
          <p className="text-gray-600">Connect with certified Ayurvedic doctors for personalized healthcare guidance</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Consultation Types */}
          <div className="lg:col-span-1">
            <ConsultationTypes 
              selectedType={selectedConsultationType}
              onTypeSelect={setSelectedConsultationType}
            />
          </div>

          {/* Center Panel - Doctor Selection & Scheduling */}
          <div className="lg:col-span-1">
            <AppointmentScheduling
              selectedDoctor={selectedDoctor}
              selectedDate={selectedDate}
              selectedTime={selectedTime}
              onDoctorSelect={setSelectedDoctor}
              onDateSelect={setSelectedDate}
              onTimeSelect={setSelectedTime}
            />
          </div>

          {/* Right Panel - Patient Information & Payment */}
          <div className="lg:col-span-1">
            <PatientInformation
              patientInfo={patientInfo}
              onPatientInfoChange={handlePatientInfoChange}
            />
          </div>
        </div>

        {/* Booking Confirmation Button */}
        <div className="mt-8 text-center">
          <Button 
            onClick={handleBookConsultation}
            disabled={!isFormComplete}
            size="lg"
            className={`px-8 py-3 text-lg ${
              isFormComplete 
                ? 'bg-green-600 hover:bg-green-700' 
                : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            {isFormComplete ? (
              <>
                <CheckCircle className="w-5 h-5 mr-2" />
                Confirm & Book Consultation
              </>
            ) : (
              'Complete All Details to Book'
            )}
          </Button>
          
          {isFormComplete && (
            <p className="text-sm text-gray-600 mt-2">
              You will be redirected to payment gateway after confirmation
            </p>
          )}
        </div>

        {/* Terms and Privacy */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            By booking a consultation, you agree to our{" "}
            <a href="#" className="text-green-600 hover:underline">Terms of Service</a> and{" "}
            <a href="#" className="text-green-600 hover:underline">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  );
}