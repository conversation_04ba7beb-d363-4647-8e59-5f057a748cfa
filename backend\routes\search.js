const express = require('express');
const {
  globalSearch,
  advancedDoctorSearch,
  advancedPatientSearch,
  advancedConsultationSearch,
} = require('../controllers/searchController');
const { protect } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Global search
router.get('/', globalSearch);

// Advanced search routes
router.get('/doctors/advanced', advancedDoctorSearch);
router.get('/patients/advanced', advancedPatientSearch);
router.get('/consultations/advanced', advancedConsultationSearch);

module.exports = router;
