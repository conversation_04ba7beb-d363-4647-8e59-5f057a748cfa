const Settings = require('../models/Settings');

// @desc    Get platform settings
// @route   GET /api/settings
// @access  Private (Admin only)
const getSettings = async (req, res, next) => {
  try {
    const settings = await Settings.getSettings();

    res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update platform settings
// @route   PUT /api/settings
// @access  Private (Admin only)
const updateSettings = async (req, res, next) => {
  try {
    let settings = await Settings.getSettings();

    // Update settings with provided data
    Object.keys(req.body).forEach(key => {
      if (settings[key] && typeof settings[key] === 'object' && !Array.isArray(settings[key])) {
        // For nested objects, merge the properties
        settings[key] = { ...settings[key], ...req.body[key] };
      } else {
        // For primitive values and arrays, replace directly
        settings[key] = req.body[key];
      }
    });

    await settings.save();

    res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getSettings,
  updateSettings,
};
