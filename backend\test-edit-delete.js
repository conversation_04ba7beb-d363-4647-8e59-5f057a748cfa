const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

let authToken = '';

// Helper function to make authenticated requests
const authenticatedRequest = (config) => {
  return axios({
    ...testConfig,
    ...config,
    headers: {
      ...testConfig.headers,
      ...(authToken && { Authorization: `Bearer ${authToken}` }),
      ...config.headers,
    },
  });
};

// Test authentication
const authenticate = async () => {
  try {
    const loginResponse = await axios({
      ...testConfig,
      method: 'POST',
      url: '/auth/login',
      data: {
        email: '<EMAIL>',
        password: 'admin123',
      },
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.token;
      console.log('✅ Authentication successful');
      return true;
    }
    return false;
  } catch (error) {
    console.log('❌ Authentication failed:', error.message);
    return false;
  }
};

// Test doctor edit operations
const testDoctorEdit = async () => {
  console.log('\n👨‍⚕️ Testing Doctor Edit Operations...');
  
  try {
    // Get a doctor to edit
    const doctorsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/doctors',
    });

    if (!doctorsResponse.data.success || doctorsResponse.data.data.length === 0) {
      console.log('❌ No doctors found to test edit');
      return;
    }

    const doctor = doctorsResponse.data.data[0];
    console.log(`📋 Testing edit for doctor: ${doctor.name}`);

    // Test valid update
    const updateData = {
      bio: 'Updated bio for testing edit functionality',
      consultationFee: doctor.consultationFee + 50,
    };

    const updateResponse = await authenticatedRequest({
      method: 'PUT',
      url: `/doctors/${doctor._id}`,
      data: updateData,
    });

    if (updateResponse.data.success) {
      console.log('✅ Doctor update successful');
      console.log(`📝 Changes tracked: ${Object.keys(updateResponse.data.changes || {}).length} fields`);
    }

    // Test invalid update (duplicate email)
    try {
      const invalidUpdateResponse = await authenticatedRequest({
        method: 'PUT',
        url: `/doctors/${doctor._id}`,
        data: { email: 'invalid-email' },
      });
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Validation error correctly caught for invalid email');
      }
    }

    // Test consultation fee change with upcoming consultations
    console.log('📅 Testing fee change restrictions...');
    
  } catch (error) {
    console.log('❌ Doctor edit test failed:', error.message);
  }
};

// Test doctor delete operations
const testDoctorDelete = async () => {
  console.log('\n🗑️ Testing Doctor Delete Operations...');
  
  try {
    // Create a test doctor for deletion
    const testDoctorData = {
      name: 'Dr. Test Delete',
      email: '<EMAIL>',
      phone: '+919999999999',
      specialization: 'Test Specialization',
      experience: 5,
      qualifications: ['BAMS'],
      registrationNumber: 'TEST001',
      consultationFee: 300,
    };

    const createResponse = await authenticatedRequest({
      method: 'POST',
      url: '/doctors',
      data: testDoctorData,
    });

    if (!createResponse.data.success) {
      console.log('❌ Failed to create test doctor');
      return;
    }

    const testDoctorId = createResponse.data.data._id;
    console.log(`📋 Created test doctor: ${testDoctorId}`);

    // Test soft delete (should work since no consultations)
    const deleteResponse = await authenticatedRequest({
      method: 'DELETE',
      url: `/doctors/${testDoctorId}`,
    });

    if (deleteResponse.data.success) {
      console.log('✅ Doctor deletion successful');
      console.log(`📊 Deletion type: ${deleteResponse.data.message}`);
    }

  } catch (error) {
    console.log('❌ Doctor delete test failed:', error.message);
  }
};

// Test patient edit operations
const testPatientEdit = async () => {
  console.log('\n👥 Testing Patient Edit Operations...');
  
  try {
    // Get a patient to edit
    const patientsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/patients',
    });

    if (!patientsResponse.data.success || patientsResponse.data.data.length === 0) {
      console.log('❌ No patients found to test edit');
      return;
    }

    const patient = patientsResponse.data.data[0];
    console.log(`📋 Testing edit for patient: ${patient.name}`);

    // Test valid update with critical medical information
    const updateData = {
      allergies: ['Peanuts', 'Shellfish'],
      bloodGroup: 'B+',
      currentMedications: [
        {
          name: 'Test Medicine',
          dosage: '10mg',
          frequency: 'Twice daily',
        }
      ],
    };

    const updateResponse = await authenticatedRequest({
      method: 'PUT',
      url: `/patients/${patient._id}`,
      data: updateData,
    });

    if (updateResponse.data.success) {
      console.log('✅ Patient update successful');
      console.log(`📝 Changes tracked: ${Object.keys(updateResponse.data.changes || {}).length} fields`);
      if (updateResponse.data.criticalChanges) {
        console.log('⚠️ Critical medical changes detected');
      }
    }

  } catch (error) {
    console.log('❌ Patient edit test failed:', error.message);
  }
};

// Test consultation edit operations
const testConsultationEdit = async () => {
  console.log('\n📅 Testing Consultation Edit Operations...');
  
  try {
    // Get a consultation to edit
    const consultationsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/consultations',
    });

    if (!consultationsResponse.data.success || consultationsResponse.data.data.length === 0) {
      console.log('❌ No consultations found to test edit');
      return;
    }

    const consultation = consultationsResponse.data.data[0];
    console.log(`📋 Testing edit for consultation: ${consultation._id}`);

    // Test valid update
    const updateData = {
      notes: 'Updated notes for testing edit functionality',
      chiefComplaint: 'Updated chief complaint',
    };

    const updateResponse = await authenticatedRequest({
      method: 'PUT',
      url: `/consultations/${consultation._id}`,
      data: updateData,
    });

    if (updateResponse.data.success) {
      console.log('✅ Consultation update successful');
      console.log(`📝 Changes tracked: ${Object.keys(updateResponse.data.changes || {}).length} fields`);
      if (updateResponse.data.hasCriticalChanges) {
        console.log('⚠️ Critical consultation changes detected');
      }
    }

    // Test invalid status transition
    try {
      await authenticatedRequest({
        method: 'PUT',
        url: `/consultations/${consultation._id}`,
        data: { status: 'Invalid Status' },
      });
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Invalid status transition correctly rejected');
      }
    }

  } catch (error) {
    console.log('❌ Consultation edit test failed:', error.message);
  }
};

// Test force delete operations
const testForceDelete = async () => {
  console.log('\n💥 Testing Force Delete Operations...');
  
  try {
    // Create a test patient for force deletion
    const testPatientData = {
      name: 'Test Force Delete Patient',
      email: '<EMAIL>',
      phone: '+919888888888',
      dateOfBirth: '1990-01-01',
      gender: 'Male',
    };

    const createResponse = await authenticatedRequest({
      method: 'POST',
      url: '/patients',
      data: testPatientData,
    });

    if (!createResponse.data.success) {
      console.log('❌ Failed to create test patient');
      return;
    }

    const testPatientId = createResponse.data.data._id;
    console.log(`📋 Created test patient: ${testPatientId}`);

    // Test force delete
    const forceDeleteResponse = await authenticatedRequest({
      method: 'DELETE',
      url: `/patients/${testPatientId}?force=true`,
    });

    if (forceDeleteResponse.data.success) {
      console.log('✅ Force delete successful');
      console.log(`📊 Deletion type: ${forceDeleteResponse.data.message}`);
    }

  } catch (error) {
    console.log('❌ Force delete test failed:', error.message);
  }
};

// Main test runner
const runEditDeleteTests = async () => {
  console.log('🚀 Starting Enhanced Edit & Delete Tests\n');
  console.log('=' .repeat(50));

  const authSuccess = await authenticate();
  
  if (!authSuccess) {
    console.log('\n❌ Authentication failed. Stopping tests.');
    return;
  }

  await testDoctorEdit();
  await testDoctorDelete();
  await testPatientEdit();
  await testConsultationEdit();
  await testForceDelete();

  console.log('\n' + '='.repeat(50));
  console.log('🎉 Enhanced Edit & Delete Tests Completed!');
};

// Run tests if this file is executed directly
if (require.main === module) {
  runEditDeleteTests().catch(console.error);
}

module.exports = { runEditDeleteTests };
