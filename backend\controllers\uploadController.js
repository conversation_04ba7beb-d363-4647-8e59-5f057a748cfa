const path = require('path');
const fs = require('fs').promises;
const { validationResult } = require('express-validator');

// @desc    Upload file
// @route   POST /api/upload
// @access  Private
const uploadFile = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded',
      });
    }

    const file = req.file;
    
    // Validate file type
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: 'File type not allowed',
        allowedTypes: ['JPEG', 'PNG', 'GIF', 'PDF', 'DOC', 'DOCX'],
      });
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: 'File size too large. Maximum size is 5MB',
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(file.originalname);
    const filename = `${timestamp}_${randomString}${extension}`;

    // Create upload directory if it doesn't exist
    const uploadDir = path.join(__dirname, '../uploads');
    try {
      await fs.access(uploadDir);
    } catch {
      await fs.mkdir(uploadDir, { recursive: true });
    }

    // Save file
    const filepath = path.join(uploadDir, filename);
    await fs.writeFile(filepath, file.buffer);

    const fileData = {
      id: `file_${timestamp}_${randomString}`,
      originalName: file.originalname,
      filename,
      mimetype: file.mimetype,
      size: file.size,
      uploadedAt: new Date(),
      uploadedBy: req.user.id,
      url: `/api/upload/files/${filename}`,
    };

    res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      data: fileData,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get uploaded file
// @route   GET /api/upload/files/:filename
// @access  Private
const getFile = async (req, res, next) => {
  try {
    const { filename } = req.params;
    const filepath = path.join(__dirname, '../uploads', filename);

    try {
      await fs.access(filepath);
    } catch {
      return res.status(404).json({
        success: false,
        message: 'File not found',
      });
    }

    // Set appropriate headers
    const extension = path.extname(filename).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };

    const contentType = mimeTypes[extension] || 'application/octet-stream';
    res.setHeader('Content-Type', contentType);

    // Stream the file
    const fileBuffer = await fs.readFile(filepath);
    res.send(fileBuffer);
  } catch (error) {
    next(error);
  }
};

// @desc    Delete uploaded file
// @route   DELETE /api/upload/files/:filename
// @access  Private
const deleteFile = async (req, res, next) => {
  try {
    const { filename } = req.params;
    const filepath = path.join(__dirname, '../uploads', filename);

    try {
      await fs.access(filepath);
      await fs.unlink(filepath);
    } catch {
      return res.status(404).json({
        success: false,
        message: 'File not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'File deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Upload avatar/profile picture
// @route   POST /api/upload/avatar
// @access  Private
const uploadAvatar = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No image uploaded',
      });
    }

    const file = req.file;
    
    // Validate image type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: 'Only image files are allowed for avatars',
        allowedTypes: ['JPEG', 'PNG', 'GIF'],
      });
    }

    // Validate file size (2MB limit for avatars)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: 'Image size too large. Maximum size is 2MB',
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(file.originalname);
    const filename = `avatar_${timestamp}_${randomString}${extension}`;

    // Create avatars directory
    const avatarDir = path.join(__dirname, '../uploads/avatars');
    try {
      await fs.access(avatarDir);
    } catch {
      await fs.mkdir(avatarDir, { recursive: true });
    }

    // Save file
    const filepath = path.join(avatarDir, filename);
    await fs.writeFile(filepath, file.buffer);

    const avatarData = {
      id: `avatar_${timestamp}_${randomString}`,
      originalName: file.originalname,
      filename,
      mimetype: file.mimetype,
      size: file.size,
      uploadedAt: new Date(),
      uploadedBy: req.user.id,
      url: `/api/upload/avatars/${filename}`,
    };

    res.status(201).json({
      success: true,
      message: 'Avatar uploaded successfully',
      data: avatarData,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get avatar
// @route   GET /api/upload/avatars/:filename
// @access  Private
const getAvatar = async (req, res, next) => {
  try {
    const { filename } = req.params;
    const filepath = path.join(__dirname, '../uploads/avatars', filename);

    try {
      await fs.access(filepath);
    } catch {
      return res.status(404).json({
        success: false,
        message: 'Avatar not found',
      });
    }

    // Set appropriate headers for images
    const extension = path.extname(filename).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
    };

    const contentType = mimeTypes[extension] || 'image/jpeg';
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

    // Stream the file
    const fileBuffer = await fs.readFile(filepath);
    res.send(fileBuffer);
  } catch (error) {
    next(error);
  }
};

// @desc    Get upload statistics
// @route   GET /api/upload/stats
// @access  Private (Admin only)
const getUploadStats = async (req, res, next) => {
  try {
    const uploadDir = path.join(__dirname, '../uploads');
    const avatarDir = path.join(uploadDir, 'avatars');

    let totalFiles = 0;
    let totalSize = 0;
    let avatarCount = 0;
    let avatarSize = 0;

    // Count regular uploads
    try {
      const files = await fs.readdir(uploadDir);
      for (const file of files) {
        if (file !== 'avatars') {
          const filepath = path.join(uploadDir, file);
          const stats = await fs.stat(filepath);
          if (stats.isFile()) {
            totalFiles++;
            totalSize += stats.size;
          }
        }
      }
    } catch (error) {
      // Directory doesn't exist or is empty
    }

    // Count avatars
    try {
      const avatars = await fs.readdir(avatarDir);
      for (const avatar of avatars) {
        const filepath = path.join(avatarDir, avatar);
        const stats = await fs.stat(filepath);
        if (stats.isFile()) {
          avatarCount++;
          avatarSize += stats.size;
        }
      }
    } catch (error) {
      // Directory doesn't exist or is empty
    }

    const stats = {
      totalFiles: totalFiles + avatarCount,
      totalSize: totalSize + avatarSize,
      regularFiles: {
        count: totalFiles,
        size: totalSize,
      },
      avatars: {
        count: avatarCount,
        size: avatarSize,
      },
      formattedSize: formatBytes(totalSize + avatarSize),
    };

    res.status(200).json({
      success: true,
      data: stats,
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to format bytes
const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

module.exports = {
  uploadFile,
  getFile,
  deleteFile,
  uploadAvatar,
  getAvatar,
  getUploadStats,
};
