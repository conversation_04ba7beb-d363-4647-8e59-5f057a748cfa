# Ayura Admin Panel & Consultation Integration

This project now includes both the admin panel and consultation booking system integrated into a single workspace with concurrent execution capabilities.

## Project Structure

```
ayura-admin-panel-new/
├── src/                          # Admin Panel Frontend
│   ├── components/
│   │   ├── consultation/         # Integrated consultation components
│   │   │   ├── ConsultationApp.tsx
│   │   │   ├── ConsultationNavigation.tsx
│   │   │   ├── Homepage.tsx
│   │   │   └── ImageWithFallback.tsx
│   │   └── ... (other admin components)
│   └── ...
├── consultation/                 # Standalone Consultation Project
│   ├── src/
│   ├── package.json
│   └── vite.config.ts
├── backend/                      # Backend API Server
│   ├── server.js
│   ├── package.json
│   └── ... (routes, models, etc.)
└── package.json                  # Main project with concurrent scripts
```

## Services & Ports

- **Admin Panel**: http://localhost:3000
- **Consultation Portal**: http://localhost:3001 (standalone)
- **Backend API**: http://localhost:5000
- **Integrated Consultation**: Available within admin panel at "Consultation Portal" tab

## Available Scripts

### Individual Services
```bash
# Run only admin panel
npm run dev:admin

# Run only consultation portal
npm run dev:consultation

# Run only backend
npm run dev:backend
```

### Combined Services
```bash
# Run admin panel + backend
npm run dev:admin-backend

# Run consultation portal + backend
npm run dev:consultation-backend

# Run all three services concurrently
npm run dev:all
# or
npm start
```

## Features

### Admin Panel (Port 3000)
- Dashboard with analytics
- Doctor management
- Patient management
- Consultation management
- Slot management
- Settings and configuration
- **NEW**: Integrated consultation portal

### Consultation Portal (Port 3001 & Integrated)
- Patient-facing consultation booking
- Doctor selection
- Appointment scheduling
- User authentication
- Consultation history

### Backend API (Port 5000)
- RESTful API for all operations
- Authentication and authorization
- Database operations
- File uploads
- Analytics and reporting

## Getting Started

1. **Install dependencies for all projects:**
   ```bash
   # Main project dependencies
   npm install

   # Consultation project dependencies
   cd consultation && npm install && cd ..

   # Backend dependencies
   cd backend && npm install && cd ..
   ```

2. **Set up environment variables:**
   ```bash
   # Copy and configure backend environment
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database and other configurations
   ```

3. **Start all services:**
   ```bash
   npm start
   ```

4. **Access the applications:**
   - Admin Panel: http://localhost:3000
   - Consultation Portal: http://localhost:3001
   - Backend API: http://localhost:5000

## ✅ Integration Status

**COMPLETED SUCCESSFULLY!**

All three services are now integrated and running concurrently:
- ✅ Admin Panel running on port 3000
- ✅ Consultation Portal running on port 3001
- ✅ Backend API running on port 5000
- ✅ Consultation Portal integrated into Admin Panel sidebar
- ✅ Concurrent execution with color-coded output
- ✅ PowerShell-compatible npm scripts

## Integration Details

### Consultation Portal in Admin Panel
The consultation portal is now accessible from the admin panel sidebar under "Consultation Portal". This provides:
- Seamless switching between admin and patient views
- Unified authentication context
- Shared UI components and styling
- Integrated user experience

### Concurrent Execution
Using `concurrently` package to run multiple services:
- Color-coded console output for each service
- Automatic process termination when one fails
- Configurable service combinations

### Development Workflow
1. Use `npm start` for full-stack development
2. Use individual scripts for focused development
3. All services auto-reload on file changes
4. Shared dependencies are managed at the root level

## Next Steps

1. **Complete consultation components**: Copy remaining consultation pages and components
2. **API integration**: Connect consultation portal to backend APIs
3. **Authentication sync**: Ensure consistent auth between admin and consultation
4. **Database schema**: Align consultation data models with admin panel
5. **Testing**: Add comprehensive tests for integrated functionality

## Troubleshooting

### Port Conflicts
If ports are already in use, modify the port numbers in:
- `vite.config.ts` (admin panel)
- `consultation/vite.config.ts` (consultation portal)
- `backend/server.js` or `backend/.env` (backend)

### Dependency Issues
If you encounter dependency conflicts:
```bash
# Clear all node_modules and reinstall
rm -rf node_modules consultation/node_modules backend/node_modules
npm install
cd consultation && npm install && cd ..
cd backend && npm install && cd ..
```

### Build Issues
For production builds:
```bash
# Build admin panel
npm run build

# Build consultation portal
cd consultation && npm run build && cd ..
```
