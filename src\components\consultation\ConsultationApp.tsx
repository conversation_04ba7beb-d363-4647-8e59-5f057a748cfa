import { useState } from "react";
import { ConsultationNavigation } from "./ConsultationNavigation";
import { Homepage } from "./Homepage";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Button } from "../ui/button";
import { ArrowLeft } from "lucide-react";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

export function ConsultationApp() {
  const [currentPage, setCurrentPage] = useState<PageType>('home');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <Homepage onNavigate={setCurrentPage} />;
      case 'consultations':
        return <ConsultationsList onNavigate={setCurrentPage} />;
      case 'book':
        return <BookConsultation onNavigate={setCurrentPage} />;
      case 'about':
        return <AboutUs onNavigate={setCurrentPage} />;
      case 'contact':
        return <ContactUs onNavigate={setCurrentPage} />;
      case 'login':
        return <Login onNavigate={setCurrentPage} onLogin={setIsLoggedIn} />;
      case 'signup':
        return <Signup onNavigate={setCurrentPage} onLogin={setIsLoggedIn} />;
      default:
        return <Homepage onNavigate={setCurrentPage} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-amber-50">
      <ConsultationNavigation 
        currentPage={currentPage} 
        onNavigate={setCurrentPage} 
        isLoggedIn={isLoggedIn}
        onLogout={() => setIsLoggedIn(false)}
      />
      {renderPage()}
    </div>
  );
}

// Placeholder components for pages not yet implemented
function ConsultationsList({ onNavigate }: { onNavigate: (page: PageType) => void }) {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => onNavigate('home')}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">Your Consultations</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Consultation History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Your consultation history will appear here...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function BookConsultation({ onNavigate }: { onNavigate: (page: PageType) => void }) {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => onNavigate('home')}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">Book a Consultation</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Schedule Your Appointment</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Consultation booking form will be implemented here...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function AboutUs({ onNavigate }: { onNavigate: (page: PageType) => void }) {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => onNavigate('home')}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">About Ayura</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Our Mission</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Learn more about our Ayurvedic practice and mission...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function ContactUs({ onNavigate }: { onNavigate: (page: PageType) => void }) {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => onNavigate('home')}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">Contact Us</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Get in Touch</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Contact information and form will be here...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function Login({ onNavigate, onLogin }: { onNavigate: (page: PageType) => void; onLogin: (status: boolean) => void }) {
  return (
    <div className="p-6 max-w-md mx-auto">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => onNavigate('home')}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">Login</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Sign In</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Login form will be implemented here...</p>
          <Button 
            onClick={() => onLogin(true)} 
            className="mt-4 bg-green-600 hover:bg-green-700"
          >
            Demo Login
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

function Signup({ onNavigate, onLogin }: { onNavigate: (page: PageType) => void; onLogin: (status: boolean) => void }) {
  return (
    <div className="p-6 max-w-md mx-auto">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => onNavigate('home')}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">Sign Up</h1>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Create Account</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Signup form will be implemented here...</p>
          <Button 
            onClick={() => onLogin(true)} 
            className="mt-4 bg-green-600 hover:bg-green-700"
          >
            Demo Signup
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
