import { useState } from "react";
import { Search, Plus, Edit, MoreHorizontal, Clock, IndianRupee, Users, Stethoscope, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";
import { Label } from "./ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Textarea } from "./ui/textarea";
import { Switch } from "./ui/switch";
import { Separator } from "./ui/separator";

interface ConsultationType {
  id: string;
  name: string;
  description: string;
  duration: number; // in minutes
  price: number;
  category: 'General' | 'Specialized' | 'Follow-up' | 'Package' | 'Emergency';
  isActive: boolean;
  features: string[];
  targetAudience: string;
  doctorSpecialization?: string;
  maxBookingsPerDay?: number;
  createdDate: string;
  totalBookings: number;
  avgRating: number;
}

const mockConsultationTypes: ConsultationType[] = [
  {
    id: "1",
    name: "General Ayurvedic Consultation",
    description: "Comprehensive health assessment based on Ayurvedic principles including prakriti analysis, dosha evaluation, and personalized treatment recommendations.",
    duration: 45,
    price: 1500,
    category: "General",
    isActive: true,
    features: ["Prakriti Analysis", "Dosha Assessment", "Diet Recommendations", "Lifestyle Guidance", "Herbal Remedies"],
    targetAudience: "All age groups seeking holistic wellness",
    maxBookingsPerDay: 10,
    createdDate: "2024-01-15",
    totalBookings: 245,
    avgRating: 4.8
  },
  {
    id: "2",
    name: "Digestive Health Consultation",
    description: "Specialized consultation focusing on digestive disorders, gut health, and Agni (digestive fire) assessment with targeted Ayurvedic treatments.",
    duration: 60,
    price: 2000,
    category: "Specialized",
    isActive: true,
    features: ["Agni Assessment", "Gut Health Analysis", "Specialized Diet Plan", "Digestive Remedies", "Follow-up Support"],
    targetAudience: "Individuals with digestive issues, IBS, acid reflux",
    doctorSpecialization: "Gastroenterology",
    maxBookingsPerDay: 6,
    createdDate: "2024-01-20",
    totalBookings: 128,
    avgRating: 4.9
  },
  {
    id: "3",
    name: "Mental Wellness & Stress Management",
    description: "Holistic approach to mental health using Ayurvedic principles, meditation techniques, and stress-reduction strategies.",
    duration: 50,
    price: 1800,
    category: "Specialized",
    isActive: true,
    features: ["Stress Assessment", "Meditation Guidance", "Pranayama Techniques", "Mental Balance Herbs", "Lifestyle Counseling"],
    targetAudience: "Individuals dealing with stress, anxiety, sleep issues",
    doctorSpecialization: "Mental Health",
    maxBookingsPerDay: 8,
    createdDate: "2024-02-01",
    totalBookings: 167,
    avgRating: 4.7
  },
  {
    id: "4",
    name: "Follow-up Consultation",
    description: "Progress review session for ongoing treatments, medication adjustments, and continued guidance for existing patients.",
    duration: 30,
    price: 800,
    category: "Follow-up",
    isActive: true,
    features: ["Progress Review", "Treatment Adjustment", "Medication Review", "Quick Q&A", "Continued Support"],
    targetAudience: "Existing patients for follow-up care",
    maxBookingsPerDay: 15,
    createdDate: "2024-01-15",
    totalBookings: 456,
    avgRating: 4.6
  },
  {
    id: "5",
    name: "Wellness Package - 3 Sessions",
    description: "Comprehensive wellness package including initial consultation, follow-up session, and final review with personalized treatment plan.",
    duration: 135, // 45+45+45
    price: 3500,
    category: "Package",
    isActive: true,
    features: ["3 Consultations", "Personalized Plan", "Diet Chart", "Exercise Routine", "Herbal Recommendations", "Email Support"],
    targetAudience: "Individuals seeking comprehensive wellness transformation",
    maxBookingsPerDay: 3,
    createdDate: "2024-02-15",
    totalBookings: 67,
    avgRating: 4.9
  },
  {
    id: "6",
    name: "Women's Health Consultation",
    description: "Specialized consultation for women's health issues including menstrual disorders, fertility, and hormonal balance using Ayurvedic approaches.",
    duration: 55,
    price: 2200,
    category: "Specialized",
    isActive: false,
    features: ["Hormonal Assessment", "Menstrual Health", "Fertility Guidance", "PCOS Management", "Pregnancy Care"],
    targetAudience: "Women of all ages with reproductive health concerns",
    doctorSpecialization: "Women's Health",
    maxBookingsPerDay: 5,
    createdDate: "2024-03-01",
    totalBookings: 89,
    avgRating: 4.8
  }
];

export function ConsultationTypesManager() {
  const [consultationTypes, setConsultationTypes] = useState<ConsultationType[]>(mockConsultationTypes);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedType, setSelectedType] = useState<ConsultationType | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [newType, setNewType] = useState<Partial<ConsultationType>>({
    name: "",
    description: "",
    duration: 45,
    price: 1500,
    category: "General",
    isActive: true,
    features: [],
    targetAudience: "",
    maxBookingsPerDay: 10
  });

  const filteredTypes = consultationTypes.filter(type => {
    const matchesSearch = type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         type.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === "all" || type.category.toLowerCase() === filterCategory.toLowerCase();
    const matchesStatus = filterStatus === "all" || 
                         (filterStatus === "active" && type.isActive) ||
                         (filterStatus === "inactive" && !type.isActive);
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const handleAddType = () => {
    const consultationType: ConsultationType = {
      ...newType as ConsultationType,
      id: (consultationTypes.length + 1).toString(),
      createdDate: new Date().toISOString().split('T')[0],
      totalBookings: 0,
      avgRating: 0,
      features: newType.features || []
    };
    setConsultationTypes([...consultationTypes, consultationType]);
    setIsAddDialogOpen(false);
    setNewType({
      name: "",
      description: "",
      duration: 45,
      price: 1500,
      category: "General",
      isActive: true,
      features: [],
      targetAudience: "",
      maxBookingsPerDay: 10
    });
  };

  const handleEditType = () => {
    if (selectedType) {
      setConsultationTypes(consultationTypes.map(t => t.id === selectedType.id ? selectedType : t));
      setIsEditDialogOpen(false);
      setSelectedType(null);
    }
  };

  const handleToggleStatus = (id: string) => {
    setConsultationTypes(consultationTypes.map(type =>
      type.id === id ? { ...type, isActive: !type.isActive } : type
    ));
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'general': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'specialized': return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'follow-up': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'package': return 'bg-green-100 text-green-700 border-green-200';
      case 'emergency': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const TypeForm = ({ type, setType, isEdit = false }: {
    type: Partial<ConsultationType>;
    setType: (type: Partial<ConsultationType>) => void;
    isEdit?: boolean;
  }) => {
    const [featuresText, setFeaturesText] = useState(type.features?.join(', ') || '');

    const handleFeaturesChange = (value: string) => {
      setFeaturesText(value);
      const featuresArray = value.split(',').map(f => f.trim()).filter(f => f.length > 0);
      setType({ ...type, features: featuresArray });
    };

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Consultation Name</Label>
            <Input
              id="name"
              value={type.name || ""}
              onChange={(e) => setType({ ...type, name: e.target.value })}
              placeholder="e.g., General Ayurvedic Consultation"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={type.category} onValueChange={(value) => setType({ ...type, category: value as any })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="General">General</SelectItem>
                <SelectItem value="Specialized">Specialized</SelectItem>
                <SelectItem value="Follow-up">Follow-up</SelectItem>
                <SelectItem value="Package">Package</SelectItem>
                <SelectItem value="Emergency">Emergency</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={type.description || ""}
            onChange={(e) => setType({ ...type, description: e.target.value })}
            rows={3}
            placeholder="Detailed description of the consultation type..."
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="duration">Duration (minutes)</Label>
            <Input
              id="duration"
              type="number"
              value={type.duration || 45}
              onChange={(e) => setType({ ...type, duration: parseInt(e.target.value) || 45 })}
              min="15"
              max="180"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="price">Price (₹)</Label>
            <Input
              id="price"
              type="number"
              value={type.price || 1500}
              onChange={(e) => setType({ ...type, price: parseInt(e.target.value) || 1500 })}
              min="0"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="target-audience">Target Audience</Label>
          <Input
            id="target-audience"
            value={type.targetAudience || ""}
            onChange={(e) => setType({ ...type, targetAudience: e.target.value })}
            placeholder="e.g., All age groups seeking holistic wellness"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="features">Features (comma-separated)</Label>
          <Textarea
            id="features"
            value={featuresText}
            onChange={(e) => handleFeaturesChange(e.target.value)}
            rows={3}
            placeholder="e.g., Prakriti Analysis, Dosha Assessment, Diet Recommendations"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="specialization">Doctor Specialization (optional)</Label>
            <Input
              id="specialization"
              value={type.doctorSpecialization || ""}
              onChange={(e) => setType({ ...type, doctorSpecialization: e.target.value })}
              placeholder="e.g., Gastroenterology, Mental Health"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="max-bookings">Max Bookings Per Day</Label>
            <Input
              id="max-bookings"
              type="number"
              value={type.maxBookingsPerDay || 10}
              onChange={(e) => setType({ ...type, maxBookingsPerDay: parseInt(e.target.value) || 10 })}
              min="1"
              max="50"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="is-active"
            checked={type.isActive || false}
            onCheckedChange={(checked) => setType({ ...type, isActive: checked })}
          />
          <Label htmlFor="is-active">Active Status</Label>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1>Consultation Types</h1>
          <p className="text-muted-foreground">Manage consultation types, pricing, and availability</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="w-4 h-4 mr-2" />
              Add Consultation Type
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Consultation Type</DialogTitle>
              <DialogDescription>
                Create a new consultation type with pricing and features.
              </DialogDescription>
            </DialogHeader>
            <TypeForm type={newType} setType={setNewType} />
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddType} className="bg-primary hover:bg-primary/90">
                Add Consultation Type
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Stethoscope className="w-8 h-8 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Total Types</p>
                <p className="text-2xl">{consultationTypes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Active Types</p>
                <p className="text-2xl">{consultationTypes.filter(t => t.isActive).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <IndianRupee className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Avg Price</p>
                <p className="text-2xl">₹{Math.round(consultationTypes.reduce((sum, t) => sum + t.price, 0) / consultationTypes.length)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-8 h-8 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Avg Duration</p>
                <p className="text-2xl">{Math.round(consultationTypes.reduce((sum, t) => sum + t.duration, 0) / consultationTypes.length)}m</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search consultation types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="specialized">Specialized</SelectItem>
                  <SelectItem value="follow-up">Follow-up</SelectItem>
                  <SelectItem value="package">Package</SelectItem>
                  <SelectItem value="emergency">Emergency</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Consultation Types List */}
      <div className="grid gap-4">
        {filteredTypes.map((type) => (
          <Card key={type.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center space-x-3 mb-2">
                        <h3>{type.name}</h3>
                        <Badge className={getCategoryColor(type.category)}>
                          {type.category}
                        </Badge>
                        <Badge variant={type.isActive ? "default" : "secondary"}>
                          {type.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3 max-w-2xl">{type.description}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span>{type.duration} minutes</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <IndianRupee className="w-4 h-4 text-muted-foreground" />
                      <span>₹{type.price}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span>{type.totalBookings} bookings</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-500">★</span>
                      <span>{type.avgRating > 0 ? type.avgRating.toFixed(1) : 'No ratings'}</span>
                    </div>
                    <div className="text-muted-foreground">
                      Max: {type.maxBookingsPerDay}/day
                    </div>
                  </div>

                  {type.features.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {type.features.slice(0, 4).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {type.features.length > 4 && (
                        <Badge variant="outline" className="text-xs">
                          +{type.features.length - 4} more
                        </Badge>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={type.isActive}
                    onCheckedChange={() => handleToggleStatus(type.id)}
                  />
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => {
                        setSelectedType(type);
                        setIsDetailsDialogOpen(true);
                      }}>
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        setSelectedType(type);
                        setIsEditDialogOpen(true);
                      }}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Type
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTypes.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">No consultation types found matching your criteria.</p>
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      {selectedType && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Consultation Type</DialogTitle>
              <DialogDescription>
                Update consultation type details and settings.
              </DialogDescription>
            </DialogHeader>
            <TypeForm 
              type={selectedType} 
              setType={setSelectedType} 
              isEdit={true} 
            />
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setSelectedType(null);
              }}>
                Cancel
              </Button>
              <Button onClick={handleEditType} className="bg-primary hover:bg-primary/90">
                Save Changes
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Details Dialog */}
      {selectedType && (
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Consultation Type Details</DialogTitle>
              <DialogDescription>
                Complete information for {selectedType.name}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Stethoscope className="w-8 h-8 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl">{selectedType.name}</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge className={getCategoryColor(selectedType.category)}>
                      {selectedType.category}
                    </Badge>
                    <Badge variant={selectedType.isActive ? "default" : "secondary"}>
                      {selectedType.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="mb-2">Description</h4>
                <p className="text-sm text-muted-foreground">{selectedType.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="mb-3">Pricing & Duration</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Price:</span> ₹{selectedType.price}</div>
                    <div><span className="font-medium">Duration:</span> {selectedType.duration} minutes</div>
                    <div><span className="font-medium">Max Bookings/Day:</span> {selectedType.maxBookingsPerDay}</div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3">Statistics</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Total Bookings:</span> {selectedType.totalBookings}</div>
                    <div><span className="font-medium">Average Rating:</span> {selectedType.avgRating > 0 ? `${selectedType.avgRating}/5` : 'No ratings yet'}</div>
                    <div><span className="font-medium">Created:</span> {selectedType.createdDate}</div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="mb-2">Target Audience</h4>
                <p className="text-sm text-muted-foreground">{selectedType.targetAudience}</p>
              </div>

              {selectedType.doctorSpecialization && (
                <div>
                  <h4 className="mb-2">Required Doctor Specialization</h4>
                  <Badge variant="outline">{selectedType.doctorSpecialization}</Badge>
                </div>
              )}

              <div>
                <h4 className="mb-3">Features Included</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedType.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <Button variant="outline" onClick={() => {
                setIsDetailsDialogOpen(false);
                setSelectedType(null);
              }}>
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}