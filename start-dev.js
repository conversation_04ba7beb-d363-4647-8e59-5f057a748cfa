#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Ayura Admin Panel Development Environment...\n');

// Check if backend node_modules exists
const backendNodeModules = path.join(__dirname, 'backend', 'node_modules');
if (!fs.existsSync(backendNodeModules)) {
  console.log('📦 Backend dependencies not found. Installing...');
  
  const installBackend = spawn('npm', ['install'], {
    cwd: path.join(__dirname, 'backend'),
    stdio: 'inherit',
    shell: true
  });

  installBackend.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Backend dependencies installed successfully!\n');
      startServers();
    } else {
      console.error('❌ Failed to install backend dependencies');
      process.exit(1);
    }
  });
} else {
  startServers();
}

function startServers() {
  console.log('🎯 Starting both Frontend and Backend servers...\n');
  
  const concurrently = spawn('npm', ['run', 'dev:full'], {
    stdio: 'inherit',
    shell: true
  });

  concurrently.on('close', (code) => {
    console.log(`\n🛑 Servers stopped with code ${code}`);
  });

  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    concurrently.kill('SIGINT');
    process.exit(0);
  });
}
