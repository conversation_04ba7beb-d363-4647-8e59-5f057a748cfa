import { useState, useEffect } from "react";
import { Search, Plus, Filter, Phone, Mail, Calendar, FileText, Eye, Edit, MoreHorizontal, RefreshCw } from "lucide-react";
import { patientsAPI } from '../utils/api';
import { toast } from 'sonner';
import { PatientForm } from './forms/PatientForm';
import { Patient, PatientFormData } from '../types/models';
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";
import { Label } from "./ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Textarea } from "./ui/textarea";
import { Separator } from "./ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

interface Patient {
  id: string;
  name: string;
  email: string;
  phone: string;
  age: number;
  gender: 'Male' | 'Female' | 'Other';
  address: string;
  dateOfBirth: string;
  registrationDate: string;
  lastConsultation: string;
  totalConsultations: number;
  healthConditions: string[];
  status: 'Active' | 'Inactive' | 'Suspended';
  avatar?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relation: string;
  };
  medicalHistory: string;
  allergies: string;
  currentMedications: string;
}

const mockPatients: Patient[] = [
  {
    id: "1",
    name: "Priya Sharma",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    age: 32,
    gender: "Female",
    address: "123 MG Road, Bangalore, Karnataka 560001",
    dateOfBirth: "1991-05-15",
    registrationDate: "2024-01-15",
    lastConsultation: "2024-08-28",
    totalConsultations: 8,
    healthConditions: ["Diabetes", "Hypertension"],
    status: "Active",
    emergencyContact: {
      name: "Rajesh Sharma",
      phone: "+91 98765 43211",
      relation: "Spouse"
    },
    medicalHistory: "Family history of diabetes. Previous surgeries: Appendectomy (2018)",
    allergies: "Penicillin, Shellfish",
    currentMedications: "Metformin 500mg twice daily"
  },
  {
    id: "2",
    name: "Arjun Patel",
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    age: 45,
    gender: "Male",
    address: "456 FC Road, Pune, Maharashtra 411005",
    dateOfBirth: "1978-12-03",
    registrationDate: "2024-02-20",
    lastConsultation: "2024-09-01",
    totalConsultations: 12,
    healthConditions: ["Arthritis", "Insomnia"],
    status: "Active",
    emergencyContact: {
      name: "Meera Patel",
      phone: "+91 87654 32110",
      relation: "Wife"
    },
    medicalHistory: "Chronic back pain since 2015. No major surgeries.",
    allergies: "None known",
    currentMedications: "Ibuprofen as needed, Melatonin 3mg"
  },
  {
    id: "3",
    name: "Lakshmi Iyer",
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    age: 28,
    gender: "Female",
    address: "789 Anna Salai, Chennai, Tamil Nadu 600002",
    dateOfBirth: "1995-08-22",
    registrationDate: "2024-03-10",
    lastConsultation: "2024-08-15",
    totalConsultations: 5,
    healthConditions: ["PCOS", "Anxiety"],
    status: "Active",
    emergencyContact: {
      name: "Ramesh Iyer",
      phone: "+91 76543 21099",
      relation: "Father"
    },
    medicalHistory: "PCOS diagnosed in 2020. Family history of thyroid issues.",
    allergies: "Latex",
    currentMedications: "Birth control pills, Vitamin D supplements"
  },
  {
    id: "4",
    name: "Vikram Singh",
    email: "<EMAIL>",
    phone: "+91 65432 10987",
    age: 52,
    gender: "Male",
    address: "321 CP, New Delhi, Delhi 110001",
    dateOfBirth: "1971-03-18",
    registrationDate: "2023-11-05",
    lastConsultation: "2024-07-20",
    totalConsultations: 15,
    healthConditions: ["High Cholesterol", "Stress"],
    status: "Inactive",
    emergencyContact: {
      name: "Sunita Singh",
      phone: "+91 65432 10988",
      relation: "Wife"
    },
    medicalHistory: "Heart palpitations in 2019. High stress job.",
    allergies: "Dust, Pollen",
    currentMedications: "Atorvastatin 20mg daily"
  }
];

export function PatientsManager() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [formLoading, setFormLoading] = useState(false);

  useEffect(() => {
    fetchPatients();
  }, []);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const response = await patientsAPI.getAll();
      if (response.success) {
        // Transform API data to match component interface
        const transformedPatients = response.data.map((patient: any) => ({
          id: patient._id,
          name: patient.name,
          email: patient.email,
          phone: patient.phone,
          age: patient.age,
          gender: patient.gender,
          address: patient.address || 'N/A',
          dateOfBirth: patient.dateOfBirth,
          registrationDate: patient.createdAt,
          lastConsultation: patient.lastVisit || 'Never',
          totalConsultations: patient.totalConsultations || 0,
          healthConditions: patient.medicalHistory?.conditions || [],
          status: patient.isActive ? 'Active' : 'Inactive',
          avatar: patient.profileImage,
          emergencyContact: {
            name: patient.emergencyContact?.name || 'N/A',
            phone: patient.emergencyContact?.phone || 'N/A',
            relationship: patient.emergencyContact?.relationship || 'N/A'
          },
          medicalInfo: {
            bloodGroup: patient.medicalHistory?.bloodGroup || 'Unknown',
            allergies: patient.medicalHistory?.allergies?.join(', ') || 'None',
            currentMedications: patient.medicalHistory?.currentMedications?.join(', ') || 'None'
          }
        }));
        setPatients(transformedPatients);
      }
    } catch (error) {
      console.error('Error fetching patients:', error);
      toast.error('Failed to load patients');
      // Fallback to mock data if API fails
      setPatients(mockPatients);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPatients();
    setRefreshing(false);
    toast.success('Patients list refreshed');
  };
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [newPatient, setNewPatient] = useState<Partial<Patient>>({
    name: "",
    email: "",
    phone: "",
    age: 0,
    gender: "Female",
    address: "",
    dateOfBirth: "",
    healthConditions: [],
    status: "Active",
    emergencyContact: { name: "", phone: "", relation: "" },
    medicalHistory: "",
    allergies: "",
    currentMedications: ""
  });

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.phone.includes(searchTerm);
    const matchesFilter = filterStatus === "all" || patient.status.toLowerCase() === filterStatus.toLowerCase();
    return matchesSearch && matchesFilter;
  });

  const handleAddPatient = async (data: PatientFormData) => {
    try {
      setFormLoading(true);
      await patientsAPI.create(data);
      toast.success('Patient created successfully');
      setIsAddDialogOpen(false);
      fetchPatients();
    } catch (error) {
      console.error('Error creating patient:', error);
      toast.error('Failed to create patient');
    } finally {
      setFormLoading(false);
    }
  };

  const handleEditPatient = async (data: PatientFormData) => {
    if (!selectedPatient) return;

    try {
      setFormLoading(true);
      await patientsAPI.update(selectedPatient.id, data);
      toast.success('Patient updated successfully');
      setIsEditDialogOpen(false);
      setSelectedPatient(null);
      fetchPatients();
    } catch (error) {
      console.error('Error updating patient:', error);
      toast.error('Failed to update patient');
    } finally {
      setFormLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-700 border-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'suspended': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const PatientForm = ({ patient, setPatient, isEdit = false }: {
    patient: Partial<Patient>;
    setPatient: (patient: Partial<Patient>) => void;
    isEdit?: boolean;
  }) => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            value={patient.name || ""}
            onChange={(e) => setPatient({ ...patient, name: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={patient.email || ""}
            onChange={(e) => setPatient({ ...patient, email: e.target.value })}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={patient.phone || ""}
            onChange={(e) => setPatient({ ...patient, phone: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="age">Age</Label>
          <Input
            id="age"
            type="number"
            value={patient.age || ""}
            onChange={(e) => setPatient({ ...patient, age: parseInt(e.target.value) || 0 })}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="gender">Gender</Label>
          <Select value={patient.gender} onValueChange={(value) => setPatient({ ...patient, gender: value as any })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Male">Male</SelectItem>
              <SelectItem value="Female">Female</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="dob">Date of Birth</Label>
          <Input
            id="dob"
            type="date"
            value={patient.dateOfBirth || ""}
            onChange={(e) => setPatient({ ...patient, dateOfBirth: e.target.value })}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="address">Address</Label>
        <Textarea
          id="address"
          value={patient.address || ""}
          onChange={(e) => setPatient({ ...patient, address: e.target.value })}
          rows={2}
        />
      </div>

      <Separator />

      <div>
        <h4 className="mb-3">Emergency Contact</h4>
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="emergency-name">Contact Name</Label>
            <Input
              id="emergency-name"
              value={patient.emergencyContact?.name || ""}
              onChange={(e) => setPatient({
                ...patient,
                emergencyContact: { ...patient.emergencyContact!, name: e.target.value }
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="emergency-phone">Contact Phone</Label>
            <Input
              id="emergency-phone"
              value={patient.emergencyContact?.phone || ""}
              onChange={(e) => setPatient({
                ...patient,
                emergencyContact: { ...patient.emergencyContact!, phone: e.target.value }
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="emergency-relation">Relation</Label>
            <Input
              id="emergency-relation"
              value={patient.emergencyContact?.relation || ""}
              onChange={(e) => setPatient({
                ...patient,
                emergencyContact: { ...patient.emergencyContact!, relation: e.target.value }
              })}
            />
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h4>Medical Information</h4>
        
        <div className="space-y-2">
          <Label htmlFor="medical-history">Medical History</Label>
          <Textarea
            id="medical-history"
            value={patient.medicalHistory || ""}
            onChange={(e) => setPatient({ ...patient, medicalHistory: e.target.value })}
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="allergies">Allergies</Label>
          <Textarea
            id="allergies"
            value={patient.allergies || ""}
            onChange={(e) => setPatient({ ...patient, allergies: e.target.value })}
            rows={2}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="medications">Current Medications</Label>
          <Textarea
            id="medications"
            value={patient.currentMedications || ""}
            onChange={(e) => setPatient({ ...patient, currentMedications: e.target.value })}
            rows={2}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">Status</Label>
        <Select value={patient.status} onValueChange={(value) => setPatient({ ...patient, status: value as any })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Inactive">Inactive</SelectItem>
            <SelectItem value="Suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1>Patients Management</h1>
          <p className="text-muted-foreground">Manage patient information and medical records</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh} variant="outline" disabled={refreshing}>
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-primary hover:bg-primary/90">
                <Plus className="w-4 h-4 mr-2" />
                Add Patient
              </Button>
            </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Patient</DialogTitle>
              <DialogDescription>
                Enter the patient's information and medical details.
              </DialogDescription>
            </DialogHeader>
            <PatientForm
              onSubmit={handleAddPatient}
              onCancel={() => setIsAddDialogOpen(false)}
              isLoading={formLoading}
            />
          </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search patients by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-40">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Patients List */}
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading patients...</span>
        </div>
      ) : filteredPatients.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No patients found matching your criteria.
        </div>
      ) : (
        <div className="grid gap-4">
          {filteredPatients.map((patient) => (
          <Card key={patient.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex space-x-4">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={patient.avatar} />
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {patient.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-3">
                      <h3>{patient.name}</h3>
                      <Badge className={getStatusColor(patient.status)}>
                        {patient.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-2">
                        <Mail className="w-4 h-4" />
                        <span>{patient.email}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="w-4 h-4" />
                        <span>{patient.phone}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>Age: {patient.age}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4" />
                        <span>{patient.totalConsultations} consultations</span>
                      </div>
                    </div>

                    {patient.healthConditions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {patient.healthConditions.map((condition, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {condition}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => {
                      setSelectedPatient(patient);
                      setIsDetailsDialogOpen(true);
                    }}>
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      setSelectedPatient(patient);
                      setIsEditDialogOpen(true);
                    }}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Patient
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardContent>
          </Card>
          ))}
        </div>
      )}

      {/* Edit Patient Dialog */}
      {selectedPatient && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Patient</DialogTitle>
              <DialogDescription>
                Update patient information and medical details.
              </DialogDescription>
            </DialogHeader>
            <PatientForm
              patient={selectedPatient}
              onSubmit={handleEditPatient}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedPatient(null);
              }}
              isLoading={formLoading}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Patient Details Dialog */}
      {selectedPatient && (
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Patient Details</DialogTitle>
              <DialogDescription>
                Complete information for {selectedPatient.name}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={selectedPatient.avatar} />
                  <AvatarFallback className="bg-primary/10 text-primary text-lg">
                    {selectedPatient.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-xl">{selectedPatient.name}</h3>
                  <Badge className={getStatusColor(selectedPatient.status)}>
                    {selectedPatient.status}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="mb-3">Contact Information</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Email:</span> {selectedPatient.email}</div>
                    <div><span className="font-medium">Phone:</span> {selectedPatient.phone}</div>
                    <div><span className="font-medium">Address:</span> {selectedPatient.address}</div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3">Personal Information</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Age:</span> {selectedPatient.age}</div>
                    <div><span className="font-medium">Gender:</span> {selectedPatient.gender}</div>
                    <div><span className="font-medium">Date of Birth:</span> {selectedPatient.dateOfBirth}</div>
                    <div><span className="font-medium">Registration:</span> {selectedPatient.registrationDate}</div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="mb-3">Emergency Contact</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Name:</span> {selectedPatient.emergencyContact.name}</div>
                  <div><span className="font-medium">Phone:</span> {selectedPatient.emergencyContact.phone}</div>
                  <div><span className="font-medium">Relation:</span> {selectedPatient.emergencyContact.relation}</div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4>Medical Information</h4>
                
                <div>
                  <p className="font-medium text-sm mb-1">Health Conditions:</p>
                  <div className="flex flex-wrap gap-1">
                    {selectedPatient.healthConditions.map((condition, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {condition}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <p className="font-medium text-sm mb-1">Medical History:</p>
                  <p className="text-sm text-muted-foreground">{selectedPatient.medicalHistory}</p>
                </div>

                <div>
                  <p className="font-medium text-sm mb-1">Allergies:</p>
                  <p className="text-sm text-muted-foreground">{selectedPatient.allergies}</p>
                </div>

                <div>
                  <p className="font-medium text-sm mb-1">Current Medications:</p>
                  <p className="text-sm text-muted-foreground">{selectedPatient.currentMedications}</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="mb-3">Consultation Summary</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Total Consultations:</span> {selectedPatient.totalConsultations}</div>
                    <div><span className="font-medium">Last Consultation:</span> {selectedPatient.lastConsultation || 'Never'}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <Button variant="outline" onClick={() => {
                setIsDetailsDialogOpen(false);
                setSelectedPatient(null);
              }}>
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}