const express = require('express');
const {
  getAvailableSlots,
  getDoctorSlots,
  generateSlots,
  getAllSlots,
  getSlotById,
  createSlot,
  updateSlot,
  deleteSlot,
  updateSlotStatus,
  bookSlot,
  cancelBooking,
  generateBulkSlots,
  getSlotStats,
} = require('../controllers/slotController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Statistics route (must be before /:id routes)
router.get('/stats', getSlotStats);

// Available slots route
router.get('/available', getAvailableSlots);

// Doctor-specific slots route
router.get('/doctor/:doctorId', getDoctorSlots);

// Bulk operations (admin only)
router.post('/generate', authorize('admin'), generateBulkSlots);

// Test endpoint for debugging
router.post('/test', (req, res) => {
  console.log('Test endpoint hit with body:', req.body);
  console.log('User:', req.user);
  res.json({
    success: true,
    message: 'Test endpoint working',
    receivedData: req.body,
    user: req.user ? { id: req.user._id, role: req.user.role } : null
  });
});

// CRUD operations for individual slots
router.route('/')
  .get(getAllSlots)
  .post(authorize('admin', 'doctor'), createSlot);

router.route('/:id')
  .get(getSlotById)
  .put(authorize('admin', 'doctor'), updateSlot)
  .delete(authorize('admin', 'doctor'), deleteSlot);

// Slot status management
router.patch('/:id/status', authorize('admin', 'doctor'), updateSlotStatus);

// Booking operations
router.post('/:id/book', bookSlot);
router.post('/:id/cancel', cancelBooking);

module.exports = router;
