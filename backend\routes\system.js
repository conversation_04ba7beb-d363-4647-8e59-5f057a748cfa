const express = require('express');
const { query } = require('express-validator');
const {
  getSystemHealth,
  getSystemMetrics,
  getSystemLogs,
  clearCache,
} = require('../controllers/systemController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication and admin role
router.use(protect);
router.use(authorize('admin'));

// Validation rules
const metricsValidation = [
  query('timeframe').optional().isIn(['1h', '6h', '24h', '7d', '30d']).withMessage('Invalid timeframe'),
];

const logsValidation = [
  query('level').optional().isIn(['all', 'error', 'warn', 'info', 'debug']).withMessage('Invalid log level'),
  query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('Limit must be between 1 and 1000'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
];

// Routes
router.get('/health', getSystemHealth);
router.get('/metrics', metricsValidation, getSystemMetrics);
router.get('/logs', logsValidation, getSystemLogs);
router.post('/cache/clear', clearCache);

module.exports = router;
