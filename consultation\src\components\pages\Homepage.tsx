import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { ImageWithFallback } from "../figma/ImageWithFallback";
import { ArrowRight, Calendar, Video, UserCheck, Star, Quote } from "lucide-react";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

interface HomepageProps {
  onNavigate: (page: PageType) => void;
}

export function Homepage({ onNavigate }: HomepageProps) {
  const steps = [
    {
      icon: UserCheck,
      title: "Choose Doctor",
      description: "Browse our certified Ayurvedic practitioners and select based on specialization and availability"
    },
    {
      icon: Calendar,
      title: "Book Appointment",
      description: "Schedule your consultation at a convenient time with secure payment options"
    },
    {
      icon: Video,
      title: "Video Consult",
      description: "Connect with your doctor through our secure video platform from the comfort of your home"
    }
  ];

  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      text: "<PERSON><PERSON> helped me manage my digestive issues naturally. The video consultation was so convenient!",
      rating: 5
    },
    {
      name: "<PERSON><PERSON>", 
      location: "Delhi",
      text: "Excellent service! The Ayurvedic treatment plan has significantly improved my stress levels.",
      rating: 5
    },
    {
      name: "<PERSON><PERSON>",
      location: "Bangalore",
      text: "Professional doctors and easy booking process. Highly recommend Ayura for natural healing.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative px-6 py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-100 via-amber-50 to-green-50 opacity-70"></div>
        <div className="relative mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Heal Naturally with{" "}
                  <span className="text-green-600">Certified Ayurvedic</span>{" "}
                  Doctors
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Book secure video consultations with trusted practitioners. 
                  Experience the wisdom of Ayurveda from the comfort of your home.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg"
                  onClick={() => onNavigate('book')}
                  className="bg-green-600 hover:bg-green-700 text-lg px-8 py-4"
                >
                  Book a Consultation
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  onClick={() => onNavigate('consultations')}
                  className="text-lg px-8 py-4 border-green-200 text-green-700 hover:bg-green-50"
                >
                  View Consultations
                </Button>
              </div>

              <div className="flex items-center space-x-8 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>500+ Happy Patients</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Certified Doctors</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Secure Platform</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative z-10">
                <ImageWithFallback
                  src="https://images.unsplash.com/photo-**********-5c350d0d3c56?auto=format&fit=crop&w=800&q=80"
                  alt="Ayurvedic consultation"
                  className="rounded-2xl shadow-2xl w-full h-96 object-cover"
                />
              </div>
              <div className="absolute -bottom-4 -right-4 w-full h-full bg-gradient-to-br from-green-200 to-amber-200 rounded-2xl -z-10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="px-6 py-20 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">How Ayura Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Simple steps to connect with Ayurvedic healing
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                <Card className="p-8 text-center h-full border-0 shadow-lg hover:shadow-xl transition-shadow">
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <step.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto text-sm font-bold">
                      {index + 1}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </Card>
                
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <ArrowRight className="w-6 h-6 text-green-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="px-6 py-20 bg-gradient-to-br from-green-50 to-amber-50">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">What Our Patients Say</h2>
            <p className="text-xl text-gray-600">Real stories from people who found healing through Ayura</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6 bg-white shadow-lg hover:shadow-xl transition-shadow">
                <div className="mb-4">
                  <Quote className="w-8 h-8 text-green-600 mb-3" />
                  <p className="text-gray-700 leading-relaxed">{testimonial.text}</p>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.name}</p>
                    <p className="text-sm text-gray-600">{testimonial.location}</p>
                  </div>
                  <div className="flex space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-6 py-20 bg-green-600">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Start Your Natural Healing Journey?
          </h2>
          <p className="text-xl text-green-100 mb-8">
            Connect with certified Ayurvedic doctors today and discover the path to wellness
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              onClick={() => onNavigate('book')}
              className="bg-white text-green-600 hover:bg-gray-100 text-lg px-8 py-4"
            >
              Book Your Consultation
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              onClick={() => onNavigate('about')}
              className="text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-green-600"
            >
              Learn More About Us
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}