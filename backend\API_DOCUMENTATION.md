# Ayura Admin Panel API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
All API endpoints (except registration and login) require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format
All API responses follow this format:

### Success Response
```json
{
  "success": true,
  "data": {...},
  "count": 10,
  "total": 100,
  "pagination": {
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Validation error 1", "Validation error 2"]
}
```

## API Endpoints

### Authentication Endpoints

#### POST /auth/login
Login user and get JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "data": {
    "id": "user_id",
    "name": "Admin User",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

#### POST /auth/register
Register a new user.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "staff",
  "phone": "+************"
}
```

#### GET /auth/me
Get current user profile.

#### PUT /auth/profile
Update user profile.

#### PUT /auth/change-password
Change user password.

### Doctors Endpoints

#### GET /doctors
Get all doctors with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search in name, specialization, qualification
- `specialization` (string): Filter by specialization
- `isActive` (boolean): Filter by active status

#### POST /doctors
Create a new doctor (Admin only).

**Request Body:**
```json
{
  "name": "Dr. John Smith",
  "email": "<EMAIL>",
  "phone": "+************",
  "specialization": "General Ayurveda",
  "experience": 10,
  "qualification": "BAMS, MD",
  "registrationNumber": "AYU001",
  "consultationFee": 500,
  "bio": "Experienced practitioner...",
  "languages": ["English", "Hindi"],
  "availability": {
    "monday": {"start": "09:00", "end": "17:00", "available": true}
  }
}
```

#### GET /doctors/:id
Get doctor by ID.

#### PUT /doctors/:id
Update doctor (Admin only).

#### DELETE /doctors/:id
Delete doctor (Admin only).

#### GET /doctors/:id/stats
Get doctor statistics.

### Patients Endpoints

#### GET /patients
Get all patients with pagination and filtering.

#### POST /patients
Create a new patient.

**Request Body:**
```json
{
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "dateOfBirth": "1990-01-01",
  "gender": "Female",
  "address": {
    "street": "123 Main St",
    "city": "Mumbai",
    "state": "Maharashtra",
    "country": "India",
    "zipCode": "400001"
  },
  "bloodGroup": "O+",
  "height": {"value": 165, "unit": "cm"},
  "weight": {"value": 60, "unit": "kg"}
}
```

#### GET /patients/:id
Get patient by ID.

#### PUT /patients/:id
Update patient.

#### DELETE /patients/:id
Delete patient (Admin only).

#### GET /patients/:id/stats
Get patient statistics.

### Consultations Endpoints

#### GET /consultations
Get all consultations with filtering.

**Query Parameters:**
- `status` (string): Filter by status
- `startDate` (date): Filter from date
- `endDate` (date): Filter to date
- `doctor` (string): Filter by doctor ID
- `patient` (string): Filter by patient ID

#### POST /consultations
Create a new consultation.

**Request Body:**
```json
{
  "patient": "patient_id",
  "doctor": "doctor_id",
  "appointmentDate": "2025-09-07",
  "appointmentTime": "10:00",
  "type": "Initial Consultation",
  "mode": "In-person",
  "fee": {
    "amount": 500,
    "currency": "INR"
  }
}
```

#### GET /consultations/:id
Get consultation by ID.

#### PUT /consultations/:id
Update consultation.

#### PUT /consultations/:id/status
Update consultation status.

#### DELETE /consultations/:id
Delete consultation (Admin only).

### Search Endpoints

#### GET /search
Global search across all entities.

**Query Parameters:**
- `q` (string): Search query (minimum 2 characters)
- `type` (string): Entity type (doctors, patients, consultations)
- `limit` (number): Maximum results per entity type

#### GET /search/doctors/advanced
Advanced doctor search with filters.

**Query Parameters:**
- `name`, `specialization`, `experience`, `minFee`, `maxFee`, `rating`, `languages`, `availability`

#### GET /search/patients/advanced
Advanced patient search with filters.

#### GET /search/consultations/advanced
Advanced consultation search with filters.

### Bulk Operations Endpoints

#### POST /bulk/doctors
Bulk import doctors (Admin only).

**Request Body:**
```json
{
  "doctors": [
    {
      "name": "Dr. John Smith",
      "email": "<EMAIL>",
      // ... other doctor fields
    }
  ]
}
```

#### POST /bulk/patients
Bulk import patients (Admin only).

#### PUT /bulk/consultations/status
Bulk update consultation status.

**Request Body:**
```json
{
  "consultationIds": ["id1", "id2", "id3"],
  "status": "Completed"
}
```

#### DELETE /bulk/consultations
Bulk delete consultations (Admin only).

#### GET /bulk/export/:type
Export data (doctors, patients, consultations) (Admin only).

**Query Parameters:**
- `format` (string): Export format (json, csv)

### Analytics Endpoints

#### GET /analytics/dashboard
Get dashboard statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalDoctors": 10,
      "totalPatients": 100,
      "totalConsultations": 500,
      "upcomingConsultations": 25
    },
    "today": {
      "consultations": 5,
      "revenue": 2500
    }
  }
}
```

#### GET /analytics/revenue
Get revenue analytics (Admin only).

#### GET /analytics/consultations
Get consultation analytics.

#### GET /analytics/doctors
Get doctor performance analytics.

#### GET /analytics/patients
Get patient analytics.

### Settings Endpoints

#### GET /settings
Get platform settings (Admin only).

#### PUT /settings
Update platform settings (Admin only).

### Slots Endpoints

#### GET /slots/available
Get available time slots for a doctor.

**Query Parameters:**
- `doctor` (string): Doctor ID
- `date` (string): Date in YYYY-MM-DD format

#### GET /slots/doctor/:doctorId
Get doctor's schedule for a date range.

**Query Parameters:**
- `startDate` (string): Start date
- `endDate` (string): End date

#### POST /slots/generate
Generate time slots (Admin only).

## Error Codes

- `400` - Bad Request (validation errors, missing parameters)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate data)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to 100 requests per 15-minute window per IP address.

## Testing

Run the API test suite:
```bash
npm test
```

This will test all endpoints and provide a comprehensive report of API functionality.
