const os = require('os');
const { validationResult } = require('express-validator');
const Consultation = require('../models/Consultation');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');

// @desc    Get system health status
// @route   GET /api/system/health
// @access  Private (Admin only)
const getSystemHealth = async (req, res, next) => {
  try {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    // System information
    const systemInfo = {
      platform: os.platform(),
      architecture: os.arch(),
      nodeVersion: process.version,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      loadAverage: os.loadavg(),
      uptime: {
        process: uptime,
        system: os.uptime(),
        formatted: formatUptime(uptime),
      },
    };

    // Memory usage
    const memoryInfo = {
      rss: memoryUsage.rss, // Resident Set Size
      heapTotal: memoryUsage.heapTotal,
      heapUsed: memoryUsage.heapUsed,
      external: memoryUsage.external,
      arrayBuffers: memoryUsage.arrayBuffers,
      formatted: {
        rss: formatBytes(memoryUsage.rss),
        heapTotal: formatBytes(memoryUsage.heapTotal),
        heapUsed: formatBytes(memoryUsage.heapUsed),
        heapUsagePercent: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(2) + '%',
      },
    };

    // Database health check
    let dbHealth = 'unknown';
    let dbStats = {};
    
    try {
      // Test database connection by counting documents
      const [doctorCount, patientCount, consultationCount] = await Promise.all([
        Doctor.countDocuments(),
        Patient.countDocuments(),
        Consultation.countDocuments(),
      ]);

      dbHealth = 'healthy';
      dbStats = {
        doctors: doctorCount,
        patients: patientCount,
        consultations: consultationCount,
        total: doctorCount + patientCount + consultationCount,
      };
    } catch (error) {
      dbHealth = 'unhealthy';
      dbStats = { error: error.message };
    }

    // Health status determination
    const healthStatus = {
      overall: 'healthy',
      database: dbHealth,
      memory: memoryUsage.heapUsed / memoryUsage.heapTotal < 0.9 ? 'healthy' : 'warning',
      uptime: uptime > 60 ? 'healthy' : 'starting',
    };

    // Determine overall health
    if (Object.values(healthStatus).some(status => status === 'unhealthy')) {
      healthStatus.overall = 'unhealthy';
    } else if (Object.values(healthStatus).some(status => status === 'warning')) {
      healthStatus.overall = 'warning';
    }

    res.status(200).json({
      success: true,
      data: {
        status: healthStatus,
        system: systemInfo,
        memory: memoryInfo,
        database: dbStats,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get system metrics
// @route   GET /api/system/metrics
// @access  Private (Admin only)
const getSystemMetrics = async (req, res, next) => {
  try {
    const { timeframe = '24h' } = req.query;

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case '1h':
        startDate = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '6h':
        startDate = new Date(now.getTime() - 6 * 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    // Get consultation metrics
    const consultationMetrics = await Consultation.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: timeframe === '1h' || timeframe === '6h' ? '%Y-%m-%d %H:00' : '%Y-%m-%d',
              date: '$createdAt',
            },
          },
          count: { $sum: 1 },
          revenue: { $sum: '$fee.amount' },
          completed: {
            $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] },
          },
          cancelled: {
            $sum: { $cond: [{ $eq: ['$status', 'Cancelled'] }, 1, 0] },
          },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Generate mock API metrics (in a real app, you'd track these)
    const apiMetrics = generateAPIMetrics(timeframe);

    // System performance metrics
    const performanceMetrics = {
      cpu: {
        usage: Math.random() * 100, // Mock CPU usage
        cores: os.cpus().length,
        loadAverage: os.loadavg(),
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usagePercent: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
      },
      disk: {
        // Mock disk usage - in production, you'd use a library like 'node-disk-info'
        total: 100 * 1024 * 1024 * 1024, // 100GB
        used: 45 * 1024 * 1024 * 1024,   // 45GB
        free: 55 * 1024 * 1024 * 1024,   // 55GB
        usagePercent: 45,
      },
    };

    res.status(200).json({
      success: true,
      data: {
        timeframe,
        consultations: consultationMetrics,
        api: apiMetrics,
        performance: performanceMetrics,
        generatedAt: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get system logs
// @route   GET /api/system/logs
// @access  Private (Admin only)
const getSystemLogs = async (req, res, next) => {
  try {
    const { level = 'all', limit = 100, page = 1 } = req.query;

    // Mock logs - in a real app, you'd read from log files or a logging service
    const logs = generateMockLogs(level, parseInt(limit), parseInt(page));

    res.status(200).json({
      success: true,
      data: {
        logs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: 500, // Mock total
        },
        filters: { level },
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Clear system cache
// @route   POST /api/system/cache/clear
// @access  Private (Admin only)
const clearCache = async (req, res, next) => {
  try {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    res.status(200).json({
      success: true,
      message: 'Cache cleared successfully',
      data: {
        clearedAt: new Date(),
        memoryBefore: process.memoryUsage(),
        memoryAfter: process.memoryUsage(),
      },
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to format uptime
const formatUptime = (seconds) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return `${days}d ${hours}h ${minutes}m ${secs}s`;
};

// Helper function to format bytes
const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// Helper function to generate mock API metrics
const generateAPIMetrics = (timeframe) => {
  const endpoints = [
    '/api/auth/login',
    '/api/consultations',
    '/api/doctors',
    '/api/patients',
    '/api/analytics/dashboard',
  ];

  return endpoints.map(endpoint => ({
    endpoint,
    requests: Math.floor(Math.random() * 1000) + 100,
    averageResponseTime: Math.floor(Math.random() * 500) + 50,
    errorRate: Math.random() * 5, // 0-5% error rate
    successRate: 95 + Math.random() * 5, // 95-100% success rate
  }));
};

// Helper function to generate mock logs
const generateMockLogs = (level, limit, page) => {
  const levels = ['error', 'warn', 'info', 'debug'];
  const messages = [
    'User authentication successful',
    'Database connection established',
    'API request processed',
    'Cache miss for key: user_123',
    'Email notification sent',
    'File upload completed',
    'Consultation created successfully',
    'Payment processed',
    'System backup completed',
    'Rate limit exceeded for IP',
  ];

  const logs = [];
  const startIndex = (page - 1) * limit;

  for (let i = 0; i < limit; i++) {
    const logLevel = levels[Math.floor(Math.random() * levels.length)];
    
    // Filter by level if specified
    if (level !== 'all' && logLevel !== level) {
      continue;
    }

    logs.push({
      id: `log_${startIndex + i + 1}`,
      level: logLevel,
      message: messages[Math.floor(Math.random() * messages.length)],
      timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      source: 'ayura-backend',
      metadata: {
        userId: Math.random() > 0.5 ? `user_${Math.floor(Math.random() * 100)}` : null,
        ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (compatible; API Client)',
      },
    });
  }

  return logs.sort((a, b) => b.timestamp - a.timestamp);
};

module.exports = {
  getSystemHealth,
  getSystemMetrics,
  getSystemLogs,
  clearCache,
};
