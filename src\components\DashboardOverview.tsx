import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "./ui/card";
import { Calendar, Users, DollarSign, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, RefreshCw, Stethoscope } from "lucide-react";
import { Progress } from "./ui/progress";
import { Button } from "./ui/button";
import { analyticsAPI } from '../utils/api';
import { toast } from 'sonner';

interface DashboardData {
  overview: {
    totalDoctors: number;
    totalPatients: number;
    totalConsultations: number;
    upcomingConsultations: number;
  };
  today: {
    consultations: number;
    revenue: number;
  };
  recentConsultations: Array<{
    _id: string;
    patient: { name: string };
    doctor: { name: string };
    type: string;
    status: string;
    appointmentDate: string;
    appointmentTime: string;
  }>;
  statusDistribution: Array<{
    _id: string;
    count: number;
  }>;
}

export function DashboardOverview() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setError(null);
      const response = await analyticsAPI.getDashboardStats();
      if (response.success) {
        setDashboardData(response.data);
        setError(null);
      } else {
        const errorMsg = 'Failed to load dashboard data';
        setError(errorMsg);
        toast.error(errorMsg);
      }
    } catch (error: any) {
      console.error('Error fetching dashboard data:', error);
      const errorMsg = error.response?.data?.message || 'Failed to load dashboard data';
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
  };

  // Calculate completion rate from status distribution
  const getCompletionRate = () => {
    if (!dashboardData?.statusDistribution) return 0;

    const total = dashboardData.statusDistribution.reduce((sum, item) => sum + item.count, 0);
    const completed = dashboardData.statusDistribution.find(item => item._id === 'Completed')?.count || 0;

    return total > 0 ? ((completed / total) * 100).toFixed(1) : 0;
  };

  // Generate stats from real data
  const getStats = () => {
    if (!dashboardData) return [];

    return [
      {
        title: "Total Consultations",
        value: dashboardData.overview.totalConsultations.toLocaleString(),
        change: `${dashboardData.today.consultations} today`,
        icon: Calendar,
        color: "text-primary",
        bgColor: "bg-primary/10"
      },
      {
        title: "Active Doctors",
        value: dashboardData.overview.totalDoctors.toString(),
        change: "Available now",
        icon: Stethoscope,
        color: "text-green-600",
        bgColor: "bg-green-100"
      },
      {
        title: "Total Patients",
        value: dashboardData.overview.totalPatients.toLocaleString(),
        change: "Registered",
        icon: Users,
        color: "text-blue-600",
        bgColor: "bg-blue-100"
      },
      {
        title: "Today's Revenue",
        value: `₹${dashboardData.today.revenue.toLocaleString()}`,
        change: `${dashboardData.today.consultations} consultations`,
        icon: DollarSign,
        color: "text-purple-600",
        bgColor: "bg-purple-100"
      }
    ];
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-6 rounded-lg border border-primary/20">
          <h1 className="text-2xl font-semibold text-primary">Dashboard</h1>
          <p className="text-muted-foreground">Loading dashboard data...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-muted rounded w-24"></div>
                <div className="h-8 w-8 bg-muted rounded-full"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-20"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error && !dashboardData) {
    return (
      <div className="p-6 space-y-6">
        <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-6 rounded-lg border border-primary/20">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-semibold text-primary">Dashboard</h1>
              <p className="text-muted-foreground">Error loading dashboard data</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="border-primary/20 hover:bg-primary/5"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Retry
            </Button>
          </div>
        </div>
        <Card className="p-8 text-center">
          <div className="text-muted-foreground">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Failed to load dashboard</h3>
            <p className="text-sm mb-4">{error}</p>
            <Button onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Try Again
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  const stats = getStats();
  const recentConsultations = dashboardData?.recentConsultations || [];

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in progress': return <Clock className="h-4 w-4 text-primary" />;
      case 'scheduled': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const formatTime = (dateString: string, timeString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 24) {
        return timeString || date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
      } else {
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return timeString || 'N/A';
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-6 rounded-lg border border-primary/20">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold text-primary">Dashboard</h1>
            <p className="text-muted-foreground">Welcome to your Ayura admin panel</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="border-primary/20 hover:bg-primary/5"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="hover:shadow-md transition-shadow border-l-4 border-primary/20 hover:border-primary">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-primary font-medium">{stat.change}</span> from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Consultations */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
            <CardTitle className="text-primary">Recent Consultations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentConsultations.length > 0 ? (
                recentConsultations.slice(0, 5).map((consultation) => (
                  <div key={consultation._id} className="flex items-center justify-between p-3 bg-muted rounded-lg hover:bg-primary/5 transition-colors">
                    <div className="flex-1">
                      <p className="font-medium">{consultation.patient?.name || 'Unknown Patient'}</p>
                      <p className="text-sm text-muted-foreground">
                        {consultation.doctor?.name || 'Unknown Doctor'} • {consultation.type}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">
                        {formatTime(consultation.appointmentDate, consultation.appointmentTime)}
                      </span>
                      {getStatusIcon(consultation.status)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No recent consultations</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Consultation Status Distribution */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="bg-gradient-to-r from-green-50 to-transparent">
            <CardTitle className="text-green-700">Consultation Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.statusDistribution && dashboardData.statusDistribution.length > 0 ? (
                dashboardData.statusDistribution.map((status) => {
                  const total = dashboardData.statusDistribution.reduce((sum, item) => sum + item.count, 0);
                  const percentage = total > 0 ? (status.count / total) * 100 : 0;

                  const getStatusColor = (statusName: string) => {
                    switch (statusName.toLowerCase()) {
                      case 'completed': return 'bg-green-500';
                      case 'in progress': return 'bg-blue-500';
                      case 'scheduled': return 'bg-orange-500';
                      case 'cancelled': return 'bg-red-500';
                      default: return 'bg-gray-500';
                    }
                  };

                  return (
                    <div key={status._id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium capitalize">{status._id}</span>
                        <span className="text-sm font-bold">{status.count}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getStatusColor(status._id)}`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-muted-foreground text-right">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No status data available</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}