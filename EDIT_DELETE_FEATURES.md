# Enhanced Edit & Delete Operations

## Overview

The Ayura Admin Panel backend now includes comprehensive edit and delete functionality with advanced features including:

- **Smart Validation**: Context-aware validation that considers existing data relationships
- **Change Tracking**: Detailed logging of what fields were modified
- **Soft Delete**: Safe deletion that preserves data integrity
- **Force Delete**: Option to permanently remove data when needed
- **Cascade Operations**: Automatic handling of related data
- **Audit Trail**: Complete logging of all operations

## Features

### 🔄 Enhanced Edit Operations

#### Doctors
- **Duplicate Prevention**: Prevents duplicate emails, phone numbers, and registration numbers
- **Consultation Fee Validation**: Restricts fee changes when upcoming consultations exist
- **Availability Validation**: Prevents availability changes that conflict with scheduled consultations
- **Change Tracking**: Tracks all field changes with before/after values
- **Audit Logging**: Logs all updates with user information

#### Patients
- **Critical Medical Data Tracking**: Special handling for allergies, medications, and blood group changes
- **Upcoming Consultation Warnings**: Alerts when critical medical data changes with upcoming appointments
- **Duplicate Prevention**: Prevents duplicate emails and phone numbers
- **Change Tracking**: Detailed tracking of all modifications

#### Consultations
- **Status Transition Validation**: Enforces valid status transitions (e.g., can't change from Completed)
- **Appointment Restriction**: Prevents date/time changes for in-progress consultations
- **Doctor Availability Check**: Validates doctor availability when changing assignments
- **Fee Protection**: Prevents fee changes for completed consultations
- **Critical Change Detection**: Identifies and logs important changes

### 🗑️ Enhanced Delete Operations

#### Smart Delete Strategy
1. **Soft Delete (Default)**: Deactivates entities while preserving data
2. **Force Delete**: Permanently removes data when explicitly requested
3. **Cascade Handling**: Automatically manages related data

#### Doctors
```
DELETE /api/doctors/:id              # Soft delete (deactivate)
DELETE /api/doctors/:id?force=true   # Force delete (permanent)
```

**Soft Delete Behavior:**
- Deactivates doctor account
- Cancels all upcoming consultations
- Preserves consultation history
- Sets deactivation timestamp and reason

**Force Delete Behavior:**
- Permanently removes doctor record
- Cancels all upcoming consultations
- Updates consultation counts

#### Patients
```
DELETE /api/patients/:id              # Soft delete (deactivate)
DELETE /api/patients/:id?force=true   # Force delete (permanent)
```

**Soft Delete Behavior:**
- Deactivates patient account
- Cancels upcoming consultations
- Preserves medical history
- Sets deactivation metadata

#### Consultations
```
DELETE /api/consultations/:id              # Standard delete with restrictions
DELETE /api/consultations/:id?force=true   # Force delete
```

**Delete Restrictions:**
- Cannot delete in-progress consultations without force
- Cannot delete completed consultations with medical data without force
- Updates doctor and patient consultation counts

## API Examples

### Edit Operations

#### Update Doctor
```bash
PUT /api/doctors/:id
Content-Type: application/json
Authorization: Bearer <token>

{
  "bio": "Updated biography",
  "consultationFee": 600,
  "languages": ["English", "Hindi", "Gujarati"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Doctor updated successfully",
  "data": { /* updated doctor object */ },
  "changes": {
    "bio": {
      "from": "Old biography",
      "to": "Updated biography"
    },
    "consultationFee": {
      "from": 500,
      "to": 600
    }
  }
}
```

#### Update Patient with Critical Medical Data
```bash
PUT /api/patients/:id
Content-Type: application/json
Authorization: Bearer <token>

{
  "bloodGroup": "B+",
  "allergies": ["Peanuts", "Shellfish"],
  "currentMedications": [
    {
      "name": "Aspirin",
      "dosage": "75mg",
      "frequency": "Daily"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Patient updated successfully",
  "data": { /* updated patient object */ },
  "changes": { /* tracked changes */ },
  "criticalChanges": {
    "bloodGroup": {
      "from": "A+",
      "to": "B+"
    },
    "allergies": {
      "from": [],
      "to": ["Peanuts", "Shellfish"]
    }
  }
}
```

### Delete Operations

#### Soft Delete Doctor
```bash
DELETE /api/doctors/:id
Authorization: Bearer <token>
```

**Response (with consultation history):**
```json
{
  "success": true,
  "message": "Doctor deactivated successfully (soft delete)",
  "data": {
    "doctor": { /* deactivated doctor object */ },
    "completedConsultations": 15,
    "cancelledUpcomingConsultations": 3
  },
  "note": "Doctor has been deactivated due to existing consultation history. Use force delete to permanently remove."
}
```

#### Force Delete Doctor
```bash
DELETE /api/doctors/:id?force=true
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Doctor deleted permanently",
  "data": {
    "deletedDoctor": {
      "id": "doctor_id",
      "name": "Dr. John Smith",
      "email": "<EMAIL>"
    },
    "cancelledConsultations": 2
  }
}
```

## Validation Rules

### Doctor Updates
- `name`: 2-100 characters
- `email`: Valid email format, unique
- `phone`: Valid mobile number, unique
- `consultationFee`: Positive number, restricted if upcoming consultations exist
- `availability`: Cannot conflict with scheduled consultations

### Patient Updates
- `name`: 2-100 characters
- `email`: Valid email format, unique
- `phone`: Valid mobile number, unique
- `bloodGroup`: Valid blood group (A+, A-, B+, B-, AB+, AB-, O+, O-)
- `allergies`: Array of strings
- `currentMedications`: Array of medication objects

### Consultation Updates
- `status`: Valid transitions only
- `appointmentDate/Time`: Cannot change if in progress
- `doctor`: Must be active and available
- `fee`: Cannot change if completed

## Error Handling

### Common Error Responses

#### Validation Error
```json
{
  "success": false,
  "message": "Validation errors",
  "errors": [
    {
      "field": "email",
      "message": "Please provide a valid email"
    }
  ]
}
```

#### Constraint Violation
```json
{
  "success": false,
  "message": "Cannot delete doctor with upcoming consultations",
  "upcomingConsultations": 3,
  "consultations": [
    {
      "id": "consultation_id",
      "patient": "John Doe",
      "date": "2025-09-10",
      "time": "10:00",
      "status": "Scheduled"
    }
  ],
  "suggestion": "Use ?force=true to force delete and cancel all upcoming consultations, or reschedule them first."
}
```

## Security & Permissions

### Authorization Requirements
- **Doctor Operations**: Admin role required for create, update, delete
- **Patient Operations**: Any authenticated user can update, admin required for delete
- **Consultation Operations**: Any authenticated user can update, admin required for delete

### Audit Trail
All operations are logged with:
- User ID who performed the action
- Timestamp of the operation
- Type of operation (create, update, delete)
- Changed fields (for updates)
- Reason for deletion (for deletes)

## Best Practices

### For Updates
1. Always validate input data
2. Check for conflicts with existing data
3. Track critical changes
4. Log all modifications
5. Provide detailed response with changes

### For Deletes
1. Use soft delete by default
2. Check for data dependencies
3. Provide clear error messages
4. Offer force delete option when appropriate
5. Update related entity counts

### Error Handling
1. Provide specific error messages
2. Include suggestions for resolution
3. Return relevant data for decision making
4. Use appropriate HTTP status codes

## Testing

Run the enhanced edit and delete tests:
```bash
npm run test:edit-delete
```

This will test:
- ✅ Doctor edit operations with validation
- ✅ Patient edit operations with critical change tracking
- ✅ Consultation edit operations with status validation
- ✅ Soft delete operations
- ✅ Force delete operations
- ✅ Error handling and validation
