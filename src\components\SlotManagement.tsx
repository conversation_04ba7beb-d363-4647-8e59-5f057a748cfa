import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import {
  Plus,

  Edit,
  Trash2,
  Copy,
  Search,
  CalendarDays,
  Users,
  CheckCircle,
  XCircle,
  RefreshCw
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import { Slot, SlotFormData, BulkSlotData, SlotFilters, Doctor } from "../types/models";
import { slotsAPI, doctorsAPI } from "../utils/api";
import { SlotForm } from "./forms/SlotForm";
import { BulkSlotForm } from "./forms/BulkSlotForm";

export function SlotManagement() {
  // State management
  const [slots, setSlots] = useState<Slot[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [formLoading, setFormLoading] = useState(false);

  // Dialog states
  const [isAddSlotOpen, setIsAddSlotOpen] = useState(false);
  const [isEditSlotOpen, setIsEditSlotOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<Slot | null>(null);

  // Filter states
  const [filters, setFilters] = useState<SlotFilters>({
    search: '',
    doctorId: 'all',
    status: 'all'
  });


  // Load data on component mount
  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('authToken');
    console.log('Auth token:', token ? 'Present' : 'Missing');

    fetchSlots();
    fetchDoctors();
  }, []);

  // Refresh slots when filters change
  useEffect(() => {
    fetchSlots();
  }, [filters]);

  // Fetch slots when filters change
  useEffect(() => {
    fetchSlots();
  }, [filters]);

  const fetchSlots = async () => {
    try {
      setLoading(true);
      // Fetch all slots without date filtering
      const params = {
        ...(filters.search && { search: filters.search }),
        ...(filters.doctorId !== 'all' && { doctorId: filters.doctorId }),
        ...(filters.status !== 'all' && { status: filters.status })
      };

      console.log('Fetching all slots with params:', params);
      const response = await slotsAPI.getAll(params);
      console.log('Fetch slots response:', response);

      if (response.success) {
        setSlots(response.data || []);
      } else {
        console.error('Failed to fetch slots:', response.message);
        toast.error(response.message || 'Failed to fetch slots');
        setSlots([]);
      }
    } catch (error) {
      console.error('Error fetching slots:', error);
      toast.error(error.message || 'Failed to fetch slots');
      setSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchDoctors = async () => {
    try {
      const response = await doctorsAPI.getAll();
      if (response.success) {
        setDoctors(response.data);
      }
    } catch (error) {
      console.error('Error fetching doctors:', error);
      toast.error('Failed to fetch doctors');
    }
  };

  // API handlers
  const handleCreateSlot = async (data: SlotFormData) => {
    try {
      setFormLoading(true);

      // Debug: Log the data being sent
      console.log('Creating slot with data:', data);

      // Validate required fields on frontend
      if (!data.doctorId || !data.date || !data.startTime || !data.endTime || !data.duration || !data.consultationType || !data.fee) {
        toast.error('Please fill in all required fields');
        return;
      }

      const response = await slotsAPI.create(data);
      console.log('Create slot response:', response);

      if (response.success) {
        toast.success('Slot created successfully');
        setIsAddSlotOpen(false);
        fetchSlots();
      } else {
        toast.error(response.message || 'Failed to create slot');
      }
    } catch (error) {
      console.error('Error creating slot:', error);
      const errorMessage = error.message || 'Failed to create slot';
      toast.error(errorMessage);
    } finally {
      setFormLoading(false);
    }
  };

  const handleEditSlot = async (data: SlotFormData) => {
    if (!selectedSlot) return;

    try {
      setFormLoading(true);
      const response = await slotsAPI.update(selectedSlot._id, data);
      if (response.success) {
        toast.success('Slot updated successfully');
        setIsEditSlotOpen(false);
        setSelectedSlot(null);
        fetchSlots();
      }
    } catch (error) {
      console.error('Error updating slot:', error);
      toast.error('Failed to update slot');
    } finally {
      setFormLoading(false);
    }
  };

  const handleBulkCreateSlots = async (data: BulkSlotData) => {
    try {
      setFormLoading(true);
      const response = await slotsAPI.generateSlots(data);
      if (response.success) {
        toast.success(`${data.dates.length * data.timeSlots.length} slots created successfully`);
        fetchSlots();
      }
    } catch (error) {
      console.error('Error creating bulk slots:', error);
      toast.error('Failed to create slots');
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteSlot = async (slotId: string) => {
    if (!confirm('Are you sure you want to delete this slot?')) return;

    try {
      const response = await slotsAPI.delete(slotId);
      if (response.success) {
        toast.success('Slot deleted successfully');
        fetchSlots();
      }
    } catch (error) {
      console.error('Error deleting slot:', error);
      toast.error('Failed to delete slot');
    }
  };

  const testAPIConnection = async () => {
    try {
      console.log('Testing API connection...');
      const response = await slotsAPI.test({ test: 'data', timestamp: new Date().toISOString() });
      console.log('Test API response:', response);
      toast.success('API connection test successful!');
    } catch (error) {
      console.error('API connection test failed:', error);
      toast.error(`API test failed: ${error.message}`);
    }
  };

  const handleCopySlot = (slot: Slot) => {
    setSelectedSlot({
      ...slot,
      _id: '', // Clear ID for new slot
      status: 'available',
      patient: undefined,
      consultation: undefined
    });
    setIsAddSlotOpen(true);
  };

  // Filter handlers
  const handleFilterChange = (field: keyof SlotFilters, value: string) => {
    setFilters((prev: SlotFilters) => ({ ...prev, [field]: value }));
  };



  const handleRefresh = () => {
    fetchSlots();
    toast.success('Slots refreshed');
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      available: { variant: "outline" as const, color: "bg-green-100 text-green-800", icon: CheckCircle },
      booked: { variant: "default" as const, color: "bg-blue-100 text-blue-800", icon: Users },
      blocked: { variant: "destructive" as const, color: "bg-red-100 text-red-800", icon: XCircle },
      cancelled: { variant: "destructive" as const, color: "bg-gray-100 text-gray-800", icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </Badge>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Slot Management</h1>
          <p className="text-muted-foreground">
            Create and manage appointment slots for doctors
            {slots.length > 0 && (
              <span className="ml-2 text-sm">
                • Showing {slots.length} slot{slots.length !== 1 ? 's' : ''}
              </span>
            )}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={testAPIConnection} variant="outline" size="sm">
            Test API
          </Button>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Dialog open={isAddSlotOpen} onOpenChange={setIsAddSlotOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Slot
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Appointment Slot</DialogTitle>
              </DialogHeader>
              <SlotForm
                slot={selectedSlot || undefined}
                onSubmit={handleCreateSlot}
                onCancel={() => {
                  setIsAddSlotOpen(false);
                  setSelectedSlot(null);
                }}
                isLoading={formLoading}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="slots" className="space-y-6">
        <TabsList>
          <TabsTrigger value="slots">All Slots</TabsTrigger>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Create</TabsTrigger>
        </TabsList>

        <TabsContent value="slots" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-4 items-center">
                {/* Search Input */}
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search slots..."
                      className="pl-9"
                      value={filters.search}
                      onChange={(e: any) => handleFilterChange('search', e.target.value)}
                    />
                  </div>
                </div>

                {/* Doctor Filter Buttons */}
                <div className="flex gap-2">
                  <span className="text-sm font-medium text-muted-foreground">Doctor:</span>
                  <Button
                    variant={filters.doctorId === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('doctorId', 'all')}
                  >
                    All
                  </Button>
                  {doctors.slice(0, 3).map((doctor: Doctor) => (
                    <Button
                      key={doctor._id}
                      variant={filters.doctorId === doctor._id ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleFilterChange('doctorId', doctor._id)}
                    >
                      {doctor.name.split(' ')[1] || doctor.name}
                    </Button>
                  ))}
                  {doctors.length > 3 && (
                    <Select value={filters.doctorId} onValueChange={(value: string) => handleFilterChange('doctorId', value)}>
                      <SelectTrigger className="w-[120px] h-8">
                        <SelectValue placeholder="More..." />
                      </SelectTrigger>
                      <SelectContent>
                        {doctors.slice(3).map((doctor: Doctor) => (
                          <SelectItem key={doctor._id} value={doctor._id}>
                            {doctor.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>

                {/* Status Filter Buttons */}
                <div className="flex gap-2">
                  <span className="text-sm font-medium text-muted-foreground">Status:</span>
                  <Button
                    variant={filters.status === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('status', 'all')}
                  >
                    All
                  </Button>
                  <Button
                    variant={filters.status === 'available' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('status', 'available')}
                  >
                    Available
                  </Button>
                  <Button
                    variant={filters.status === 'booked' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('status', 'booked')}
                  >
                    Booked
                  </Button>
                  <Button
                    variant={filters.status === 'blocked' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('status', 'blocked')}
                  >
                    Blocked
                  </Button>
                  <Button
                    variant={filters.status === 'cancelled' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('status', 'cancelled')}
                  >
                    Cancelled
                  </Button>
                </div>

                {/* Clear Filters Button */}
                {(filters.search || filters.doctorId !== 'all' || filters.status !== 'all') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFilters({ search: '', doctorId: 'all', status: 'all' })}
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Slots Table */}
          <Card>
            <CardHeader>
              <CardTitle>Appointment Slots</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p>Loading slots...</p>
                  </div>
                </div>
              ) : slots.length === 0 ? (
                <div className="text-center py-8">
                  <CalendarDays className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No slots found</p>
                  <p className="text-sm text-muted-foreground">Try adjusting your filters or create new slots</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-2">Slot ID</th>
                        <th className="text-left py-3 px-2">Doctor</th>
                        <th className="text-left py-3 px-2">Date & Time</th>
                        <th className="text-left py-3 px-2">Duration</th>
                        <th className="text-left py-3 px-2">Type</th>
                        <th className="text-left py-3 px-2">Fee</th>
                        <th className="text-left py-3 px-2">Status</th>
                        <th className="text-left py-3 px-2">Patient</th>
                        <th className="text-left py-3 px-2">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {slots.map((slot: Slot) => (
                        <tr key={slot._id} className="border-b hover:bg-muted/50">
                          <td className="py-3 px-2 font-mono text-sm">{slot._id.slice(-6)}</td>
                          <td className="py-3 px-2">
                            <div>
                              <div className="font-medium">{slot.doctor.name}</div>
                              <div className="text-sm text-muted-foreground">{slot.doctor.specialization}</div>
                            </div>
                          </td>
                          <td className="py-3 px-2">
                            <div>
                              <div>{format(new Date(slot.date), 'MMM dd, yyyy')}</div>
                              <div className="text-sm text-muted-foreground">{slot.startTime} - {slot.endTime}</div>
                            </div>
                          </td>
                          <td className="py-3 px-2">{slot.duration} min</td>
                          <td className="py-3 px-2">{slot.consultationType}</td>
                          <td className="py-3 px-2">₹{slot.fee.amount}</td>
                          <td className="py-3 px-2">{getStatusBadge(slot.status)}</td>
                          <td className="py-3 px-2">
                            {slot.patient ? (
                              <div>
                                <div className="font-medium">{slot.patient.name}</div>
                                <div className="text-sm text-muted-foreground">{slot.patient.phone}</div>
                              </div>
                            ) : (
                              "-"
                            )}
                          </td>
                          <td className="py-3 px-2">
                            <div className="flex space-x-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setSelectedSlot(slot);
                                  setIsEditSlotOpen(true);
                                }}
                                disabled={slot.status === 'booked'}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleCopySlot(slot)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleDeleteSlot(slot._id)}
                                disabled={slot.status === 'booked'}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Calendar View</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground py-8">
                <CalendarDays className="mx-auto h-12 w-12 mb-4" />
                <p>Calendar view coming soon...</p>
                <p className="text-sm">Visual representation of all slots across dates</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Slot Creation</CardTitle>
            </CardHeader>
            <CardContent>
              <BulkSlotForm
                onSubmit={handleBulkCreateSlots}
                onCancel={() => {}}
                isLoading={formLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Slot Dialog */}
      {selectedSlot && (
        <Dialog open={isEditSlotOpen} onOpenChange={setIsEditSlotOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Appointment Slot</DialogTitle>
            </DialogHeader>
            <SlotForm
              slot={selectedSlot}
              onSubmit={handleEditSlot}
              onCancel={() => {
                setIsEditSlotOpen(false);
                setSelectedSlot(null);
              }}
              isLoading={formLoading}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

