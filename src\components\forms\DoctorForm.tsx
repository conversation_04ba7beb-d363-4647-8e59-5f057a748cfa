import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { X, Plus } from 'lucide-react';
import { Doctor, DoctorFormData, FormErrors } from '../../types/models';

interface DoctorFormProps {
  doctor?: Doctor;
  onSubmit: (data: DoctorFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const specializations = [
  'Panchakarma Specialist',
  'Ayurvedic Nutrition',
  'Pulse Diagnosis Expert',
  'Herbal Medicine',
  'Ayurvedic Dermatology',
  'Ayurvedic Cardiology',
  'Ayurvedic Gynecology',
  'General Ayurveda'
];

const languages = [
  'English', 'Hindi', 'Tamil', 'Telugu', 'Malayalam', 'Kannada', 
  'Bengali', 'Marathi', 'Gujarati', 'Punjabi', 'Urdu'
];

const defaultAvailability = {
  monday: { start: '09:00', end: '17:00', available: true },
  tuesday: { start: '09:00', end: '17:00', available: true },
  wednesday: { start: '09:00', end: '17:00', available: true },
  thursday: { start: '09:00', end: '17:00', available: true },
  friday: { start: '09:00', end: '17:00', available: true },
  saturday: { start: '09:00', end: '13:00', available: true },
  sunday: { start: '09:00', end: '13:00', available: false }
};

export function DoctorForm({ doctor, onSubmit, onCancel, isLoading = false }: DoctorFormProps) {
  const [formData, setFormData] = useState<DoctorFormData>({
    name: doctor?.name || '',
    email: doctor?.email || '',
    phone: doctor?.phone || '',
    specialization: doctor?.specialization || '',
    experience: doctor?.experience || 0,
    consultationFee: doctor?.consultationFee || 0,
    languages: doctor?.languages || [],
    availability: doctor?.availability || defaultAvailability,
    qualifications: doctor?.qualifications || [],
    registrationNumber: doctor?.registrationNumber || '',
    bio: doctor?.bio || '',
    isActive: doctor?.isActive ?? true,
    profileImage: doctor?.profileImage || ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [newQualification, setNewQualification] = useState('');
  const [newLanguage, setNewLanguage] = useState('');

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone is required';
    if (!formData.specialization) newErrors.specialization = 'Specialization is required';
    if (formData.experience < 0) newErrors.experience = 'Experience must be positive';
    if (formData.consultationFee < 0) newErrors.consultationFee = 'Fee must be positive';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    // Phone validation
    const phoneRegex = /^[0-9]{10}$/;
    if (formData.phone && !phoneRegex.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = 'Phone must be 10 digits';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  const handleInputChange = (field: keyof DoctorFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addQualification = () => {
    if (newQualification.trim() && !formData.qualifications.includes(newQualification.trim())) {
      setFormData(prev => ({
        ...prev,
        qualifications: [...prev.qualifications, newQualification.trim()]
      }));
      setNewQualification('');
    }
  };

  const removeQualification = (qualification: string) => {
    setFormData(prev => ({
      ...prev,
      qualifications: prev.qualifications.filter(q => q !== qualification)
    }));
  };

  const addLanguage = () => {
    if (newLanguage && !formData.languages.includes(newLanguage)) {
      setFormData(prev => ({
        ...prev,
        languages: [...prev.languages, newLanguage]
      }));
      setNewLanguage('');
    }
  };

  const removeLanguage = (language: string) => {
    setFormData(prev => ({
      ...prev,
      languages: prev.languages.filter(l => l !== language)
    }));
  };

  const updateAvailability = (day: keyof typeof defaultAvailability, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: {
          ...prev.availability[day],
          [field]: value
        }
      }
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>
            <div>
              <Label htmlFor="phone">Phone *</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
            </div>
            <div>
              <Label htmlFor="specialization">Specialization *</Label>
              <Select
                value={formData.specialization}
                onValueChange={(value) => handleInputChange('specialization', value)}
              >
                <SelectTrigger className={errors.specialization ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select specialization" />
                </SelectTrigger>
                <SelectContent>
                  {specializations.map((spec) => (
                    <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.specialization && <p className="text-red-500 text-sm mt-1">{errors.specialization}</p>}
            </div>
            <div>
              <Label htmlFor="experience">Experience (years) *</Label>
              <Input
                id="experience"
                type="number"
                min="0"
                value={formData.experience}
                onChange={(e) => handleInputChange('experience', parseInt(e.target.value) || 0)}
                className={errors.experience ? 'border-red-500' : ''}
              />
              {errors.experience && <p className="text-red-500 text-sm mt-1">{errors.experience}</p>}
            </div>
            <div>
              <Label htmlFor="consultationFee">Consultation Fee (₹) *</Label>
              <Input
                id="consultationFee"
                type="number"
                min="0"
                value={formData.consultationFee}
                onChange={(e) => handleInputChange('consultationFee', parseInt(e.target.value) || 0)}
                className={errors.consultationFee ? 'border-red-500' : ''}
              />
              {errors.consultationFee && <p className="text-red-500 text-sm mt-1">{errors.consultationFee}</p>}
            </div>
            <div>
              <Label htmlFor="registrationNumber">Registration Number</Label>
              <Input
                id="registrationNumber"
                value={formData.registrationNumber}
                onChange={(e) => handleInputChange('registrationNumber', e.target.value)}
                placeholder="Medical registration number"
                className={errors.registrationNumber ? 'border-red-500' : ''}
              />
              {errors.registrationNumber && <p className="text-red-500 text-sm mt-1">{errors.registrationNumber}</p>}
            </div>
          </div>
          
          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              placeholder="Brief description about the doctor..."
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleInputChange('isActive', checked)}
            />
            <Label htmlFor="isActive">Active Status</Label>
          </div>
        </CardContent>
      </Card>

      {/* Languages */}
      <Card>
        <CardHeader>
          <CardTitle>Languages</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {formData.languages.map((language) => (
              <Badge key={language} variant="secondary" className="flex items-center gap-1">
                {language}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeLanguage(language)}
                />
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Select value={newLanguage} onValueChange={setNewLanguage}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                {languages.filter(lang => !formData.languages.includes(lang)).map((lang) => (
                  <SelectItem key={lang} value={lang}>{lang}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button type="button" onClick={addLanguage} disabled={!newLanguage}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Qualifications */}
      <Card>
        <CardHeader>
          <CardTitle>Qualifications</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {formData.qualifications.map((qualification) => (
              <Badge key={qualification} variant="secondary" className="flex items-center gap-1">
                {qualification}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeQualification(qualification)}
                />
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newQualification}
              onChange={(e) => setNewQualification(e.target.value)}
              placeholder="Add qualification (e.g., BAMS, MD Ayurveda)"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addQualification())}
            />
            <Button type="button" onClick={addQualification} disabled={!newQualification.trim()}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Availability */}
      <Card>
        <CardHeader>
          <CardTitle>Availability</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(formData.availability).map(([day, schedule]) => (
              <div key={day} className="flex items-center gap-4">
                <div className="w-20">
                  <Label className="capitalize">{day}</Label>
                </div>
                <Checkbox
                  checked={schedule.available}
                  onCheckedChange={(checked) => updateAvailability(day as keyof typeof defaultAvailability, 'available', checked)}
                />
                {schedule.available && (
                  <>
                    <Input
                      type="time"
                      value={schedule.start}
                      onChange={(e) => updateAvailability(day as keyof typeof defaultAvailability, 'start', e.target.value)}
                      className="w-32"
                    />
                    <span>to</span>
                    <Input
                      type="time"
                      value={schedule.end}
                      onChange={(e) => updateAvailability(day as keyof typeof defaultAvailability, 'end', e.target.value)}
                      className="w-32"
                    />
                  </>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : doctor ? 'Update Doctor' : 'Create Doctor'}
        </Button>
      </div>
    </form>
  );
}
