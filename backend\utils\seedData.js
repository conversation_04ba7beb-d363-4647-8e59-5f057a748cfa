const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const Settings = require('../models/Settings');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('📦 MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
};

const seedUsers = async () => {
  try {
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('✅ Admin user already exists');
      return;
    }

    // Create admin user
    const adminUser = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      phone: '+919876543210',
    });

    console.log('✅ Admin user created:', adminUser.email);
  } catch (error) {
    console.error('❌ Error seeding users:', error.message);
  }
};

const seedDoctors = async () => {
  try {
    const doctorCount = await Doctor.countDocuments();
    if (doctorCount > 0) {
      console.log('✅ Doctors already exist');
      return;
    }

    const sampleDoctors = [
      {
        name: 'Dr. Rajesh Kumar',
        email: '<EMAIL>',
        phone: '+919876543211',
        specialization: 'General Ayurveda',
        experience: 15,
        qualifications: ['BAMS', 'MD (Ayurveda)'],
        registrationNumber: 'AYU001',
        consultationFee: 500,
        bio: 'Experienced Ayurvedic practitioner specializing in general wellness and preventive care.',
        languages: ['English', 'Hindi', 'Sanskrit'],
        availability: {
          monday: { start: '09:00', end: '17:00', available: true },
          tuesday: { start: '09:00', end: '17:00', available: true },
          wednesday: { start: '09:00', end: '17:00', available: true },
          thursday: { start: '09:00', end: '17:00', available: true },
          friday: { start: '09:00', end: '17:00', available: true },
          saturday: { start: '09:00', end: '13:00', available: true },
          sunday: { start: '', end: '', available: false },
        },
        rating: 4.5,
      },
      {
        name: 'Dr. Priya Sharma',
        email: '<EMAIL>',
        phone: '+919876543212',
        specialization: 'Panchakarma',
        experience: 12,
        qualifications: ['BAMS', 'MD (Panchakarma)'],
        registrationNumber: 'AYU002',
        consultationFee: 600,
        bio: 'Specialist in Panchakarma therapies and detoxification treatments.',
        languages: ['English', 'Hindi'],
        availability: {
          monday: { start: '10:00', end: '18:00', available: true },
          tuesday: { start: '10:00', end: '18:00', available: true },
          wednesday: { start: '10:00', end: '18:00', available: true },
          thursday: { start: '10:00', end: '18:00', available: true },
          friday: { start: '10:00', end: '18:00', available: true },
          saturday: { start: '10:00', end: '14:00', available: true },
          sunday: { start: '', end: '', available: false },
        },
        rating: 4.8,
      },
      {
        name: 'Dr. Amit Patel',
        email: '<EMAIL>',
        phone: '+919876543213',
        specialization: 'Kayachikitsa',
        experience: 10,
        qualifications: ['BAMS', 'MD (Kayachikitsa)'],
        registrationNumber: 'AYU003',
        consultationFee: 450,
        bio: 'Expert in internal medicine and chronic disease management through Ayurveda.',
        languages: ['English', 'Hindi', 'Gujarati'],
        availability: {
          monday: { start: '08:00', end: '16:00', available: true },
          tuesday: { start: '08:00', end: '16:00', available: true },
          wednesday: { start: '08:00', end: '16:00', available: true },
          thursday: { start: '08:00', end: '16:00', available: true },
          friday: { start: '08:00', end: '16:00', available: true },
          saturday: { start: '', end: '', available: false },
          sunday: { start: '', end: '', available: false },
        },
        rating: 4.3,
      },
    ];

    await Doctor.insertMany(sampleDoctors);
    console.log('✅ Sample doctors created');
  } catch (error) {
    console.error('❌ Error seeding doctors:', error.message);
  }
};

const seedPatients = async () => {
  try {
    const patientCount = await Patient.countDocuments();
    if (patientCount > 0) {
      console.log('✅ Patients already exist');
      return;
    }

    const samplePatients = [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+************',
        dateOfBirth: new Date('1985-06-15'),
        gender: 'Male',
        address: {
          street: '123 Main Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          country: 'India',
          zipCode: '400001',
        },
        bloodGroup: 'O+',
        height: { value: 175, unit: 'cm' },
        weight: { value: 70, unit: 'kg' },
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+************',
        dateOfBirth: new Date('1990-03-22'),
        gender: 'Female',
        address: {
          street: '456 Park Avenue',
          city: 'Delhi',
          state: 'Delhi',
          country: 'India',
          zipCode: '110001',
        },
        bloodGroup: 'A+',
        height: { value: 165, unit: 'cm' },
        weight: { value: 60, unit: 'kg' },
      },
    ];

    await Patient.insertMany(samplePatients);
    console.log('✅ Sample patients created');
  } catch (error) {
    console.error('❌ Error seeding patients:', error.message);
  }
};

const seedSettings = async () => {
  try {
    const existingSettings = await Settings.findOne();
    if (existingSettings) {
      console.log('✅ Settings already exist');
      return;
    }

    await Settings.create({});
    console.log('✅ Default settings created');
  } catch (error) {
    console.error('❌ Error seeding settings:', error.message);
  }
};

const seedAll = async () => {
  await connectDB();
  
  console.log('🌱 Starting database seeding...');
  
  await seedUsers();
  await seedDoctors();
  await seedPatients();
  await seedSettings();
  
  console.log('🎉 Database seeding completed!');
  
  mongoose.connection.close();
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedAll();
}

module.exports = { seedAll };
