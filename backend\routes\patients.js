const express = require('express');
const { body } = require('express-validator');
const {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  getPatientStats,
} = require('../controllers/patientController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules for creating patient
const patientValidation = [
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('phone').isMobilePhone().withMessage('Please provide a valid phone number'),
  body('dateOfBirth').isISO8601().withMessage('Please provide a valid date of birth'),
  body('gender').isIn(['Male', 'Female', 'Other']).withMessage('Invalid gender'),
];

// Validation rules for updating patient (optional fields)
const patientUpdateValidation = [
  body('name').optional().trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('phone').optional().isMobilePhone().withMessage('Please provide a valid phone number'),
  body('dateOfBirth').optional().isISO8601().withMessage('Please provide a valid date of birth'),
  body('gender').optional().isIn(['Male', 'Female', 'Other']).withMessage('Invalid gender'),
  body('bloodGroup').optional().isIn(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']).withMessage('Invalid blood group'),
  body('allergies').optional().isArray().withMessage('Allergies must be an array'),
  body('currentMedications').optional().isArray().withMessage('Current medications must be an array'),
  body('medicalHistory').optional().isArray().withMessage('Medical history must be an array'),
  body('height.value').optional().isFloat({ min: 0 }).withMessage('Height must be a positive number'),
  body('weight.value').optional().isFloat({ min: 0 }).withMessage('Weight must be a positive number'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
];

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(getPatients)
  .post(patientValidation, createPatient);

router.route('/:id')
  .get(getPatient)
  .put(patientUpdateValidation, updatePatient)
  .delete(authorize('admin'), deletePatient);

router.get('/:id/stats', getPatientStats);

module.exports = router;
