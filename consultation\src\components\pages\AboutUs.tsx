import { Card } from "../ui/card";
import { ImageWithFallback } from "../figma/ImageWithFallback";
import { Leaf, Heart, Users, Award, Globe, Shield } from "lucide-react";

type PageType = 'home' | 'consultations' | 'book' | 'about' | 'contact' | 'login' | 'signup';

interface AboutUsProps {
  onNavigate: (page: PageType) => void;
}

const teamMembers = [
  {
    name: "Dr. <PERSON><PERSON>",
    role: "Founder & Chief Medical Officer",
    image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?auto=format&fit=crop&w=400&q=80",
    bio: "With 25+ years in Ayurveda, Dr<PERSON> is passionate about making traditional healing accessible to everyone through technology.",
    qualifications: "BAMS, MD Ayurveda, PhD"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Co-Founder & CEO",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=400&q=80",
    bio: "Former healthcare technology executive with a mission to bridge the gap between ancient wisdom and modern convenience.",
    qualifications: "MBA Healthcare, MS Computer Science"
  },
  {
    name: "Dr. <PERSON><PERSON>",
    role: "Head of Clinical Operations",
    image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?auto=format&fit=crop&w=400&q=80",
    bio: "Oversees quality assurance and clinical protocols to ensure the highest standards of patient care across all consultations.",
    qualifications: "BAMS, MD Ayurveda, Clinical Research"
  },
  {
    name: "Anita Patel",
    role: "Head of Technology",
    image: "https://images.unsplash.com/photo-1594824087757-56fc670ad2b4?auto=format&fit=crop&w=400&q=80",
    bio: "Leading the development of secure, user-friendly platforms that make healthcare accessible to patients across India.",
    qualifications: "MS Engineering, Healthcare Tech Expert"
  }
];

const values = [
  {
    icon: Heart,
    title: "Patient-Centered Care",
    description: "Every decision we make prioritizes patient wellbeing and healing"
  },
  {
    icon: Shield,
    title: "Trust & Security",
    description: "Protecting patient privacy with enterprise-grade security standards"
  },
  {
    icon: Globe,
    title: "Accessibility",
    description: "Making quality Ayurvedic care available to everyone, everywhere"
  },
  {
    icon: Award,
    title: "Excellence",
    description: "Partnering only with certified, experienced Ayurvedic practitioners"
  }
];

const principles = [
  {
    title: "Vata",
    description: "Movement and circulation - we help balance the body's vital energy",
    color: "from-blue-400 to-purple-500"
  },
  {
    title: "Pitta", 
    description: "Transformation and metabolism - supporting digestive and metabolic health",
    color: "from-orange-400 to-red-500"
  },
  {
    title: "Kapha",
    description: "Structure and stability - building immunity and physical strength",
    color: "from-green-400 to-teal-500"
  }
];

export function AboutUs({ onNavigate }: AboutUsProps) {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="px-6 py-20 bg-gradient-to-br from-green-50 to-amber-50">
        <div className="mx-auto max-w-7xl text-center">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center">
              <Leaf className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold text-gray-900 mb-6">About Ayura</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Bridging ancient Ayurvedic wisdom with modern technology to make natural healing 
            accessible, convenient, and effective for everyone.
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="px-6 py-20 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                To democratize access to authentic Ayurvedic healthcare by connecting patients 
                with certified practitioners through secure, convenient video consultations.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                We believe that everyone deserves access to natural, holistic healing that 
                treats the root cause, not just symptoms.
              </p>
            </div>
            <div className="relative">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=800&q=80"
                alt="Ayurvedic herbs and treatments"
                className="rounded-2xl shadow-xl w-full h-80 object-cover"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mt-20">
            <div className="relative order-2 lg:order-1">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-**********-0eb30cd8c063?auto=format&fit=crop&w=800&q=80"
                alt="Modern healthcare technology"
                className="rounded-2xl shadow-xl w-full h-80 object-cover"
              />
            </div>
            <div className="order-1 lg:order-2">
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Vision</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                To become the world's most trusted platform for Ayurvedic healthcare, 
                where traditional wisdom meets cutting-edge technology.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                We envision a future where preventive, natural healthcare is the norm, 
                and every person has access to personalized wellness guidance.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Ayurvedic Principles */}
      <section className="px-6 py-20 bg-gradient-to-br from-green-50 to-amber-50">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Ayurvedic Principles We Follow</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our practice is rooted in the three fundamental doshas that govern all aspects of health
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {principles.map((principle, index) => (
              <Card key={index} className="p-8 text-center bg-white shadow-lg hover:shadow-xl transition-shadow">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${principle.color} mx-auto mb-6 flex items-center justify-center`}>
                  <span className="text-white font-bold text-lg">{principle.title[0]}</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{principle.title}</h3>
                <p className="text-gray-600 leading-relaxed">{principle.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="px-6 py-20 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600">The principles that guide everything we do</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <value.icon className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="px-6 py-20 bg-gray-50">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
            <p className="text-xl text-gray-600">
              Passionate experts dedicated to bringing you the best in Ayurvedic healthcare
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="p-6 text-center bg-white shadow-lg hover:shadow-xl transition-shadow">
                <ImageWithFallback
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-lg font-semibold text-gray-900 mb-1">{member.name}</h3>
                <p className="text-green-600 font-medium mb-2">{member.role}</p>
                <p className="text-sm text-gray-600 mb-3 leading-relaxed">{member.bio}</p>
                <p className="text-xs text-gray-500">{member.qualifications}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="px-6 py-20 bg-green-600">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
            <div>
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-green-100">Happy Patients</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-green-100">Expert Doctors</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">2000+</div>
              <div className="text-green-100">Consultations</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">4.8</div>
              <div className="text-green-100">Average Rating</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}