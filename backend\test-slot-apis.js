const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
};

let authToken = '';
let testDoctorId = '';
let testSlotId = '';

// Test data
const testSlotData = {
  doctorId: '', // Will be set after getting doctors
  date: '2024-12-20',
  startTime: '10:00',
  endTime: '10:30',
  duration: 30,
  consultationType: 'General Consultation',
  fee: {
    amount: 800,
    currency: 'INR'
  },
  notes: 'Test slot created via API'
};

const testBulkSlotData = {
  doctorId: '', // Will be set after getting doctors
  dates: ['2024-12-21', '2024-12-22'],
  timeSlots: ['09:00', '09:30', '10:00'],
  duration: 30,
  consultationType: 'General Consultation',
  fee: {
    amount: 800,
    currency: 'INR'
  },
  isRecurring: false
};

// Helper function to make API requests
async function apiRequest(method, endpoint, data = null) {
  try {
    const config = {
      ...testConfig,
      method,
      url: `${BASE_URL}${endpoint}`,
      ...(authToken && { headers: { ...testConfig.headers, Authorization: `Bearer ${authToken}` } }),
      ...(data && { data }),
    };

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testLogin() {
  console.log('🔐 Testing login...');
  try {
    const response = await apiRequest('post', '/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (response.success && response.token) {
      authToken = response.token;
      console.log('✅ Login successful');
      return true;
    } else {
      console.log('❌ Login failed - no token received');
      return false;
    }
  } catch (error) {
    console.log('❌ Login failed');
    return false;
  }
}

async function getDoctors() {
  console.log('👨‍⚕️ Getting doctors...');
  try {
    const response = await apiRequest('get', '/doctors');
    if (response.success && response.data.length > 0) {
      testDoctorId = response.data[0]._id;
      testSlotData.doctorId = testDoctorId;
      testBulkSlotData.doctorId = testDoctorId;
      console.log(`✅ Found ${response.data.length} doctors, using: ${response.data[0].name}`);
      return true;
    } else {
      console.log('❌ No doctors found');
      return false;
    }
  } catch (error) {
    console.log('❌ Failed to get doctors');
    return false;
  }
}

async function testCreateSlot() {
  console.log('➕ Testing create slot...');
  try {
    const response = await apiRequest('post', '/slots', testSlotData);
    if (response.success && response.data) {
      testSlotId = response.data._id;
      console.log('✅ Slot created successfully:', response.data._id);
      return true;
    } else {
      console.log('❌ Failed to create slot');
      return false;
    }
  } catch (error) {
    console.log('❌ Create slot failed');
    return false;
  }
}

async function testGetAllSlots() {
  console.log('📋 Testing get all slots...');
  try {
    const response = await apiRequest('get', '/slots');
    if (response.success) {
      console.log(`✅ Retrieved ${response.data.length} slots`);
      return true;
    } else {
      console.log('❌ Failed to get slots');
      return false;
    }
  } catch (error) {
    console.log('❌ Get slots failed');
    return false;
  }
}

async function testGetSlotById() {
  console.log('🔍 Testing get slot by ID...');
  if (!testSlotId) {
    console.log('❌ No test slot ID available');
    return false;
  }
  
  try {
    const response = await apiRequest('get', `/slots/${testSlotId}`);
    if (response.success && response.data) {
      console.log('✅ Retrieved slot by ID:', response.data._id);
      return true;
    } else {
      console.log('❌ Failed to get slot by ID');
      return false;
    }
  } catch (error) {
    console.log('❌ Get slot by ID failed');
    return false;
  }
}

async function testUpdateSlot() {
  console.log('✏️ Testing update slot...');
  if (!testSlotId) {
    console.log('❌ No test slot ID available');
    return false;
  }
  
  try {
    const updateData = {
      ...testSlotData,
      notes: 'Updated test slot via API',
      duration: 45,
      endTime: '10:45'
    };
    
    const response = await apiRequest('put', `/slots/${testSlotId}`, updateData);
    if (response.success && response.data) {
      console.log('✅ Slot updated successfully');
      return true;
    } else {
      console.log('❌ Failed to update slot');
      return false;
    }
  } catch (error) {
    console.log('❌ Update slot failed');
    return false;
  }
}

async function testBulkCreateSlots() {
  console.log('📦 Testing bulk create slots...');
  try {
    const response = await apiRequest('post', '/slots/generate', testBulkSlotData);
    if (response.success && response.data) {
      console.log(`✅ Bulk created ${response.data.length} slots`);
      return true;
    } else {
      console.log('❌ Failed to bulk create slots');
      return false;
    }
  } catch (error) {
    console.log('❌ Bulk create slots failed');
    return false;
  }
}

async function testGetSlotStats() {
  console.log('📊 Testing get slot statistics...');
  try {
    const response = await apiRequest('get', '/slots/stats');
    if (response.success && response.data) {
      console.log('✅ Retrieved slot statistics:', {
        totalSlots: response.data.totalSlots,
        availableSlots: response.data.availableSlots,
        bookedSlots: response.data.bookedSlots,
        utilizationRate: response.data.utilizationRate + '%'
      });
      return true;
    } else {
      console.log('❌ Failed to get slot statistics');
      return false;
    }
  } catch (error) {
    console.log('❌ Get slot statistics failed');
    return false;
  }
}

async function testDeleteSlot() {
  console.log('🗑️ Testing delete slot...');
  if (!testSlotId) {
    console.log('❌ No test slot ID available');
    return false;
  }
  
  try {
    const response = await apiRequest('delete', `/slots/${testSlotId}`);
    if (response.success) {
      console.log('✅ Slot deleted successfully');
      return true;
    } else {
      console.log('❌ Failed to delete slot');
      return false;
    }
  } catch (error) {
    console.log('❌ Delete slot failed');
    return false;
  }
}

// Main test runner
async function runSlotAPITests() {
  console.log('🧪 Starting Slot API Tests...\n');
  
  const tests = [
    { name: 'Login', fn: testLogin },
    { name: 'Get Doctors', fn: getDoctors },
    { name: 'Create Slot', fn: testCreateSlot },
    { name: 'Get All Slots', fn: testGetAllSlots },
    { name: 'Get Slot by ID', fn: testGetSlotById },
    { name: 'Update Slot', fn: testUpdateSlot },
    { name: 'Bulk Create Slots', fn: testBulkCreateSlots },
    { name: 'Get Slot Statistics', fn: testGetSlotStats },
    { name: 'Delete Slot', fn: testDeleteSlot },
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      failed++;
    }
    console.log(''); // Add spacing between tests
  }
  
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('🎉 All slot API tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runSlotAPITests().catch(console.error);
}

module.exports = { runSlotAPITests };
