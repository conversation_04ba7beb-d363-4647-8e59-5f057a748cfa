const mongoose = require('mongoose');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const Consultation = require('../models/Consultation');
const Slot = require('../models/Slot');

// @desc    Get available slots for a doctor on a specific date
// @route   GET /api/slots/available?doctor=:doctorId&date=:date
// @access  Private
const getAvailableSlots = async (req, res, next) => {
  try {
    const { doctor: doctorId, date } = req.query;

    if (!doctorId || !date) {
      return res.status(400).json({
        success: false,
        message: 'Doctor ID and date are required',
      });
    }

    const doctor = await Doctor.findById(doctorId);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    // Get day of week
    const dateObj = new Date(date);
    const dayOfWeek = dateObj.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const dayName = dayOfWeek;
    const dayAvailability = doctor.availability[dayName];

    if (!dayAvailability || !dayAvailability.available) {
      return res.status(200).json({
        success: true,
        data: [],
        message: 'Doctor is not available on this day',
      });
    }

    // Generate time slots
    const slots = generateTimeSlots(dayAvailability.start, dayAvailability.end, 30); // 30-minute slots

    // Get booked slots for this date
    const bookedConsultations = await Consultation.find({
      doctor: doctorId,
      appointmentDate: date,
      status: { $in: ['Scheduled', 'In Progress'] },
    }).select('appointmentTime');

    const bookedTimes = bookedConsultations.map(consultation => consultation.appointmentTime);

    // Filter out booked slots
    const availableSlots = slots.filter(slot => !bookedTimes.includes(slot));

    res.status(200).json({
      success: true,
      data: availableSlots,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get doctor's schedule for a date range
// @route   GET /api/slots/doctor/:doctorId?startDate=:startDate&endDate=:endDate
// @access  Private
const getDoctorSlots = async (req, res, next) => {
  try {
    const { doctorId } = req.params;
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required',
      });
    }

    const doctor = await Doctor.findById(doctorId);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    // Get consultations for the date range
    const consultations = await Consultation.find({
      doctor: doctorId,
      appointmentDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      },
    }).populate('patient', 'name email phone').sort({ appointmentDate: 1, appointmentTime: 1 });

    res.status(200).json({
      success: true,
      data: consultations,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Generate time slots for a doctor
// @route   POST /api/slots/generate
// @access  Private (Admin only)
const generateSlots = async (req, res, next) => {
  try {
    const { doctorId, date, startTime, endTime, slotDuration = 30 } = req.body;

    if (!doctorId || !date || !startTime || !endTime) {
      return res.status(400).json({
        success: false,
        message: 'Doctor ID, date, start time, and end time are required',
      });
    }

    const doctor = await Doctor.findById(doctorId);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found',
      });
    }

    const slots = generateTimeSlots(startTime, endTime, slotDuration);

    res.status(200).json({
      success: true,
      data: slots,
      message: `Generated ${slots.length} time slots`,
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to generate time slots
const generateTimeSlots = (startTime, endTime, duration) => {
  const slots = [];
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);

  let current = new Date(start);
  while (current < end) {
    const timeString = current.toTimeString().slice(0, 5);
    slots.push(timeString);
    current.setMinutes(current.getMinutes() + duration);
  }

  return slots;
};

// @desc    Get all slots with filtering
// @route   GET /api/slots
// @access  Private
const getAllSlots = async (req, res, next) => {
  try {
    const {
      search,
      doctorId,
      status,
      date,
      dateFrom,
      dateTo,
      consultationType,
      page = 1,
      limit = 50
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const doctors = await Doctor.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { specialization: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      const doctorIds = doctors.map(doc => doc._id);
      query.doctor = { $in: doctorIds };
    }

    // Filter by doctor
    if (doctorId && doctorId !== 'all') {
      query.doctor = doctorId;
    }

    // Filter by status
    if (status && status !== 'all') {
      query.status = status;
    }

    // Filter by specific date
    if (date) {
      query.date = new Date(date);
    }

    // Filter by date range
    if (dateFrom && dateTo) {
      query.date = {
        $gte: new Date(dateFrom),
        $lte: new Date(dateTo)
      };
    }

    // Filter by consultation type
    if (consultationType) {
      query.consultationType = consultationType;
    }

    // Execute query with pagination
    const slots = await Slot.find(query)
      .populate('doctor', 'name specialization experience consultationFee')
      .populate('patient', 'name phone email')
      .populate('consultation', 'status symptoms')
      .sort({ date: 1, startTime: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Slot.countDocuments(query);

    res.status(200).json({
      success: true,
      data: slots,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get a single slot by ID
// @route   GET /api/slots/:id
// @access  Private
const getSlotById = async (req, res, next) => {
  try {
    const slot = await Slot.findById(req.params.id)
      .populate('doctor', 'name specialization experience consultationFee availability')
      .populate('patient', 'name phone email age gender')
      .populate('consultation', 'status symptoms diagnosis prescription');

    if (!slot) {
      return res.status(404).json({
        success: false,
        message: 'Slot not found'
      });
    }

    res.status(200).json({
      success: true,
      data: slot
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create a new slot
// @route   POST /api/slots
// @access  Private (Admin/Doctor)
const createSlot = async (req, res, next) => {
  try {
    const {
      doctorId,
      date,
      startTime,
      endTime,
      duration,
      consultationType,
      fee,
      notes,
      isRecurring,
      recurringPattern
    } = req.body;

    // Validate required fields
    if (!doctorId || !date || !startTime || !endTime || !duration || !consultationType || !fee) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Verify doctor exists
    const doctor = await Doctor.findById(doctorId);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found'
      });
    }

    // Create slot data
    const slotData = {
      doctor: doctorId,
      date: new Date(date),
      startTime,
      endTime,
      duration,
      consultationType,
      fee,
      notes,
      isRecurring,
      recurringPattern,
      metadata: {
        createdBy: req.user.id,
        source: 'manual'
      }
    };

    const slot = await Slot.create(slotData);

    // Populate the created slot
    await slot.populate('doctor', 'name specialization consultationFee');

    res.status(201).json({
      success: true,
      data: slot,
      message: 'Slot created successfully'
    });
  } catch (error) {
    if (error.message.includes('overlaps')) {
      return res.status(400).json({
        success: false,
        message: 'Slot overlaps with an existing slot'
      });
    }
    next(error);
  }
};

// @desc    Update a slot
// @route   PUT /api/slots/:id
// @access  Private (Admin/Doctor)
const updateSlot = async (req, res, next) => {
  try {
    let slot = await Slot.findById(req.params.id);

    if (!slot) {
      return res.status(404).json({
        success: false,
        message: 'Slot not found'
      });
    }

    // Check if slot can be updated (not booked or in the past)
    if (slot.status === 'booked') {
      return res.status(400).json({
        success: false,
        message: 'Cannot update a booked slot'
      });
    }

    if (slot.isPast) {
      return res.status(400).json({
        success: false,
        message: 'Cannot update a past slot'
      });
    }

    // Update metadata
    req.body.metadata = {
      ...slot.metadata,
      updatedBy: req.user.id
    };

    slot = await Slot.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    }).populate('doctor', 'name specialization consultationFee');

    res.status(200).json({
      success: true,
      data: slot,
      message: 'Slot updated successfully'
    });
  } catch (error) {
    if (error.message.includes('overlaps')) {
      return res.status(400).json({
        success: false,
        message: 'Updated slot overlaps with an existing slot'
      });
    }
    next(error);
  }
};

// @desc    Delete a slot
// @route   DELETE /api/slots/:id
// @access  Private (Admin/Doctor)
const deleteSlot = async (req, res, next) => {
  try {
    const slot = await Slot.findById(req.params.id);

    if (!slot) {
      return res.status(404).json({
        success: false,
        message: 'Slot not found'
      });
    }

    // Check if slot can be deleted (not booked)
    if (slot.status === 'booked') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete a booked slot. Cancel the booking first.'
      });
    }

    await Slot.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Slot deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update slot status
// @route   PATCH /api/slots/:id/status
// @access  Private (Admin/Doctor)
const updateSlotStatus = async (req, res, next) => {
  try {
    const { status } = req.body;

    if (!status || !['available', 'blocked', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Valid status is required (available, blocked, cancelled)'
      });
    }

    const slot = await Slot.findById(req.params.id);

    if (!slot) {
      return res.status(404).json({
        success: false,
        message: 'Slot not found'
      });
    }

    // Handle status transitions
    if (slot.status === 'booked' && status !== 'cancelled') {
      return res.status(400).json({
        success: false,
        message: 'Booked slots can only be cancelled'
      });
    }

    slot.status = status;
    slot.metadata.updatedBy = req.user.id;

    if (status === 'cancelled' && slot.status === 'booked') {
      slot.bookingDetails.cancelledAt = new Date();
      slot.bookingDetails.cancelledBy = req.user.id;
      slot.patient = null;
      slot.consultation = null;
    }

    await slot.save();

    res.status(200).json({
      success: true,
      data: slot,
      message: `Slot status updated to ${status}`
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Book a slot
// @route   POST /api/slots/:id/book
// @access  Private
const bookSlot = async (req, res, next) => {
  try {
    const { patientId, consultationId } = req.body;

    if (!patientId) {
      return res.status(400).json({
        success: false,
        message: 'Patient ID is required'
      });
    }

    const slot = await Slot.findById(req.params.id);

    if (!slot) {
      return res.status(404).json({
        success: false,
        message: 'Slot not found'
      });
    }

    // Verify patient exists
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Book the slot using the instance method
    await slot.book(patientId, consultationId, req.user.id);

    // Populate and return the updated slot
    await slot.populate('patient', 'name phone email');
    await slot.populate('doctor', 'name specialization');

    res.status(200).json({
      success: true,
      data: slot,
      message: 'Slot booked successfully'
    });
  } catch (error) {
    if (error.message.includes('not available')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
};

// @desc    Cancel a booking
// @route   POST /api/slots/:id/cancel
// @access  Private
const cancelBooking = async (req, res, next) => {
  try {
    const { reason } = req.body;

    const slot = await Slot.findById(req.params.id);

    if (!slot) {
      return res.status(404).json({
        success: false,
        message: 'Slot not found'
      });
    }

    // Cancel the booking using the instance method
    await slot.cancel(reason, req.user.id);

    res.status(200).json({
      success: true,
      data: slot,
      message: 'Booking cancelled successfully'
    });
  } catch (error) {
    if (error.message.includes('Only booked slots')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
};

// @desc    Generate bulk slots
// @route   POST /api/slots/generate
// @access  Private (Admin)
const generateBulkSlots = async (req, res, next) => {
  try {
    const {
      doctorId,
      dates,
      timeSlots,
      duration,
      consultationType,
      fee,
      isRecurring,
      recurringWeeks = 1
    } = req.body;

    // Validate required fields
    if (!doctorId || !dates || !timeSlots || !duration || !consultationType || !fee) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Verify doctor exists
    const doctor = await Doctor.findById(doctorId);
    if (!doctor) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found'
      });
    }

    const slotsToCreate = [];
    const weeks = isRecurring ? recurringWeeks : 1;

    // Generate slots for each week
    for (let week = 0; week < weeks; week++) {
      for (const dateStr of dates) {
        const baseDate = new Date(dateStr);
        const slotDate = new Date(baseDate);
        slotDate.setDate(baseDate.getDate() + (week * 7));

        for (const timeSlot of timeSlots) {
          const startTime = timeSlot;
          const endTime = calculateEndTime(startTime, duration);

          slotsToCreate.push({
            doctor: doctorId,
            date: slotDate,
            startTime,
            endTime,
            duration,
            consultationType,
            fee,
            isRecurring,
            metadata: {
              createdBy: req.user.id,
              source: 'bulk'
            }
          });
        }
      }
    }

    // Create all slots
    const createdSlots = await Slot.insertMany(slotsToCreate);

    res.status(201).json({
      success: true,
      data: createdSlots,
      message: `Successfully created ${createdSlots.length} slots`
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Some slots overlap with existing slots'
      });
    }
    next(error);
  }
};

// @desc    Get slot statistics
// @route   GET /api/slots/stats
// @access  Private
const getSlotStats = async (req, res, next) => {
  try {
    const { doctorId, startDate, endDate } = req.query;

    let matchQuery = {};

    if (doctorId) {
      matchQuery.doctor = mongoose.Types.ObjectId.createFromHexString(doctorId);
    }

    if (startDate && endDate) {
      matchQuery.date = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const stats = await Slot.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalSlots: { $sum: 1 },
          availableSlots: {
            $sum: { $cond: [{ $eq: ['$status', 'available'] }, 1, 0] }
          },
          bookedSlots: {
            $sum: { $cond: [{ $eq: ['$status', 'booked'] }, 1, 0] }
          },
          blockedSlots: {
            $sum: { $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0] }
          },
          cancelledSlots: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          },
          totalRevenue: {
            $sum: { $cond: [{ $eq: ['$status', 'booked'] }, '$fee.amount', 0] }
          }
        }
      }
    ]);

    const result = stats[0] || {
      totalSlots: 0,
      availableSlots: 0,
      bookedSlots: 0,
      blockedSlots: 0,
      cancelledSlots: 0,
      totalRevenue: 0
    };

    // Calculate utilization rate
    result.utilizationRate = result.totalSlots > 0
      ? ((result.bookedSlots / result.totalSlots) * 100).toFixed(2)
      : 0;

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to calculate end time
const calculateEndTime = (startTime, duration) => {
  const [hours, minutes] = startTime.split(':').map(Number);
  const startMinutes = hours * 60 + minutes;
  const endMinutes = startMinutes + duration;
  const endHours = Math.floor(endMinutes / 60);
  const endMins = endMinutes % 60;
  return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
};

module.exports = {
  getAvailableSlots,
  getDoctorSlots,
  generateSlots,
  getAllSlots,
  getSlotById,
  createSlot,
  updateSlot,
  deleteSlot,
  updateSlotStatus,
  bookSlot,
  cancelBooking,
  generateBulkSlots,
  getSlotStats,
};
