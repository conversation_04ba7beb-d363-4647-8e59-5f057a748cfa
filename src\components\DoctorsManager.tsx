import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { UserPlus, Search, Star, Clock, Calendar, Edit, Trash2, Eye, RefreshCw, Plus } from "lucide-react";
import { doctorsAPI } from '../utils/api';
import { toast } from 'sonner';
import { DoctorForm } from './forms/DoctorForm';
import { Doctor, DoctorFormData } from '../types/models';

export function DoctorsManager() {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [specializationFilter, setSpecializationFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [viewingDoctor, setViewingDoctor] = useState<Doctor | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctors = async () => {
    try {
      setLoading(true);
      const response = await doctorsAPI.getAll();
      if (response.success) {
        setDoctors(response.data);
      }
    } catch (error) {
      console.error('Error fetching doctors:', error);
      toast.error('Failed to load doctors');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDoctors();
    setRefreshing(false);
    toast.success('Doctors list refreshed');
  };

  const handleDeleteDoctor = async (doctor: Doctor) => {
    const confirmMessage = `Are you sure you want to delete Dr. ${doctor.name}?\n\nThis action cannot be undone. The doctor's profile and all associated data will be permanently removed.`;

    if (window.confirm(confirmMessage)) {
      try {
        const response = await doctorsAPI.delete(doctor._id);

        if (response.success) {
          toast.success(`Dr. ${doctor.name} deleted successfully`);
          fetchDoctors();
        } else {
          toast.error(response.message || 'Failed to delete doctor');
        }
      } catch (error: any) {
        console.error('Error deleting doctor:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to delete doctor';
        toast.error(errorMessage);
      }
    }
  };

  const handleCreateDoctor = async (data: DoctorFormData) => {
    try {
      setFormLoading(true);
      await doctorsAPI.create(data);
      toast.success('Doctor created successfully');
      setIsCreateDialogOpen(false);
      fetchDoctors();
    } catch (error) {
      console.error('Error creating doctor:', error);
      toast.error('Failed to create doctor');
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateDoctor = async (data: DoctorFormData) => {
    if (!selectedDoctor) return;

    try {
      setFormLoading(true);
      await doctorsAPI.update(selectedDoctor._id, data);
      toast.success('Doctor updated successfully');
      setIsEditDialogOpen(false);
      setSelectedDoctor(null);
      fetchDoctors();
    } catch (error) {
      console.error('Error updating doctor:', error);
      toast.error('Failed to update doctor');
    } finally {
      setFormLoading(false);
    }
  };

  const handleEditDoctor = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setIsEditDialogOpen(true);
  };

  const handleViewDoctor = (doctor: Doctor) => {
    setViewingDoctor(doctor);
    setIsViewDialogOpen(true);
  };

  const filteredDoctors = doctors.filter(doctor => {
    // Text search filter
    const matchesSearch = !searchTerm || (
      doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.specialization.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doctor.registrationNumber && doctor.registrationNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      doctor.languages.some(lang => lang.toLowerCase().includes(searchTerm.toLowerCase())) ||
      doctor.qualifications.some(qual => qual.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    // Specialization filter
    const matchesSpecialization = !specializationFilter || specializationFilter === 'all' || doctor.specialization === specializationFilter;

    return matchesSearch && matchesSpecialization;
  });

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "outline"}>
        {isActive ? "Active" : "Inactive"}
      </Badge>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Doctors</h1>
          <p className="text-muted-foreground">
            Manage doctor profiles and schedules • {filteredDoctors.length} of {doctors.length} doctors shown
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh} variant="outline" disabled={refreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Doctor
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Doctor</DialogTitle>
              </DialogHeader>
              <DoctorForm
                onSubmit={handleCreateDoctor}
                onCancel={() => setIsCreateDialogOpen(false)}
                isLoading={formLoading}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search doctors by name, specialization, email, phone, registration number, languages, or qualifications..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-64">
              <Select value={specializationFilter} onValueChange={setSpecializationFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by specialization" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Specializations</SelectItem>
                  <SelectItem value="Panchakarma Specialist">Panchakarma Specialist</SelectItem>
                  <SelectItem value="Ayurvedic Nutrition">Ayurvedic Nutrition</SelectItem>
                  <SelectItem value="Pulse Diagnosis Expert">Pulse Diagnosis Expert</SelectItem>
                  <SelectItem value="Herbal Medicine">Herbal Medicine</SelectItem>
                  <SelectItem value="Ayurvedic Dermatology">Ayurvedic Dermatology</SelectItem>
                  <SelectItem value="Ayurvedic Cardiology">Ayurvedic Cardiology</SelectItem>
                  <SelectItem value="Ayurvedic Gynecology">Ayurvedic Gynecology</SelectItem>
                  <SelectItem value="General Ayurveda">General Ayurveda</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Doctors Grid */}
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading doctors...</span>
        </div>
      ) : filteredDoctors.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No doctors found matching your search criteria.
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDoctors.map((doctor) => (
            <Card key={doctor._id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={doctor.profileImage} alt={doctor.name} />
                      <AvatarFallback>{doctor.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{doctor.name}</h3>
                    <p className="text-sm text-muted-foreground">{doctor.specialization}</p>
                  </div>
                </div>
                {getStatusBadge(doctor.isActive)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Experience</p>
                  <p className="font-medium">{doctor.experience} years</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Rating</p>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{doctor.rating || 'N/A'}</span>
                  </div>
                </div>
                <div>
                  <p className="text-muted-foreground">Consultations</p>
                  <p className="font-medium">{doctor.totalConsultations}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Fees</p>
                  <p className="font-medium">₹{doctor.consultationFee}</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Contact</p>
                <div className="mt-1 space-y-1">
                  <p className="text-sm">{doctor.email}</p>
                  <p className="text-sm">{doctor.phone}</p>
                  {doctor.registrationNumber && (
                    <p className="text-sm text-muted-foreground">Reg: {doctor.registrationNumber}</p>
                  )}
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Languages</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {doctor.languages.map((lang) => (
                    <Badge key={lang} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>

              {doctor.qualifications && doctor.qualifications.length > 0 && (
                <div>
                  <p className="text-sm text-muted-foreground">Qualifications</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {doctor.qualifications.map((qual) => (
                      <Badge key={qual} variant="secondary" className="text-xs">
                        {qual}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex space-x-2 pt-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => handleViewDoctor(doctor)}
                >
                  <Eye className="mr-1 h-3 w-3" />
                  View
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => toast.info(`Opening schedule for ${doctor.name}`)}
                >
                  <Calendar className="mr-1 h-3 w-3" />
                  Schedule
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEditDoctor(doctor)}
                  title="Edit Doctor"
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDeleteDoctor(doctor)}
                  title="Delete Doctor"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
          ))}
        </div>
      )}

      {/* Edit Doctor Dialog */}
      {selectedDoctor && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Doctor</DialogTitle>
            </DialogHeader>
            <DoctorForm
              doctor={selectedDoctor}
              onSubmit={handleUpdateDoctor}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedDoctor(null);
              }}
              isLoading={formLoading}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Doctor Profile View Dialog */}
      {isViewDialogOpen && viewingDoctor && (
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-y-auto">
            <DialogHeader className="pb-6">
              <DialogTitle className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <UserPlus className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">{viewingDoctor.name}</h2>
                    <p className="text-lg text-muted-foreground">{viewingDoctor.specialization}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant={viewingDoctor.isActive ? "default" : "secondary"}>
                        {viewingDoctor.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {viewingDoctor.registrationNumber && (
                        <Badge variant="outline">
                          Reg: {viewingDoctor.registrationNumber}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-primary">₹{viewingDoctor.consultationFee}</div>
                  <div className="text-sm text-muted-foreground">Consultation Fee</div>
                </div>
              </DialogTitle>
            </DialogHeader>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email</label>
                    <p className="text-sm">{viewingDoctor.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Phone</label>
                    <p className="text-sm">{viewingDoctor.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Experience</label>
                    <p className="text-sm">{viewingDoctor.experience} years</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Consultation Fee</label>
                    <p className="text-sm">₹{viewingDoctor.consultationFee}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Years of Experience</label>
                    <p className="text-sm font-semibold">{viewingDoctor.experience} years in practice</p>
                  </div>
                </CardContent>
              </Card>

              {/* Professional Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Professional Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {viewingDoctor.qualifications && viewingDoctor.qualifications.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Qualifications</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {viewingDoctor.qualifications.map((qual) => (
                          <Badge key={qual} variant="secondary" className="text-xs">
                            {qual}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {viewingDoctor.languages && viewingDoctor.languages.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Languages</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {viewingDoctor.languages.map((lang) => (
                          <Badge key={lang} variant="outline" className="text-xs">
                            {lang}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Rating</label>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm">{viewingDoctor.rating || 0}/5</span>
                      <span className="text-xs text-muted-foreground">
                        ({viewingDoctor.totalConsultations || 0} consultations)
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Statistics & Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Total Consultations</label>
                    <p className="text-2xl font-bold text-primary">{viewingDoctor.totalConsultations || 0}</p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Patient Rating</label>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center gap-1">
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        <span className="text-xl font-semibold">{viewingDoctor.rating || 0}</span>
                        <span className="text-sm text-muted-foreground">/5</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Member Since</label>
                    <p className="text-sm">
                      {viewingDoctor.joinedDate
                        ? new Date(viewingDoctor.joinedDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })
                        : 'N/A'
                      }
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Account Status</label>
                    <div className="mt-1">
                      <Badge
                        variant={viewingDoctor.isActive ? "default" : "secondary"}
                        className={viewingDoctor.isActive ? "bg-green-500 hover:bg-green-600" : ""}
                      >
                        {viewingDoctor.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Bio */}
              {viewingDoctor.bio && (
                <Card className="lg:col-span-3">
                  <CardHeader>
                    <CardTitle className="text-lg">About</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {viewingDoctor.bio}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Availability */}
              {viewingDoctor.availability && (
                <Card className="lg:col-span-3">
                  <CardHeader>
                    <CardTitle className="text-lg">Weekly Availability</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
                      {Object.entries(viewingDoctor.availability).map(([day, schedule]) => {
                        const daySchedule = schedule as { available: boolean; start: string; end: string };
                        return (
                          <div key={day} className="text-center p-3 border rounded-lg">
                            <div className="font-medium text-sm capitalize mb-1">{day}</div>
                            {daySchedule.available ? (
                              <div className="text-xs text-muted-foreground">
                                <div>{daySchedule.start}</div>
                                <div>to</div>
                                <div>{daySchedule.end}</div>
                              </div>
                            ) : (
                              <div className="text-xs text-muted-foreground">Unavailable</div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            <div className="flex justify-between items-center mt-8 pt-6 border-t">
              <div className="text-sm text-muted-foreground">
                Last updated: {viewingDoctor.updatedAt
                  ? new Date(viewingDoctor.updatedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })
                  : 'N/A'
                }
              </div>
              <div className="flex gap-3">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Close
                </Button>
                <Button onClick={() => {
                  setIsViewDialogOpen(false);
                  handleEditDoctor(viewingDoctor);
                }} className="bg-primary hover:bg-primary/90">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Doctor
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}