const { validationResult } = require('express-validator');
const Patient = require('../models/Patient');
const Consultation = require('../models/Consultation');

// Helper function to track changes
const getChangedFields = (original, updated) => {
  const changes = {};
  const excludeFields = ['_id', '__v', 'createdAt', 'updatedAt'];

  for (const key in updated) {
    if (!excludeFields.includes(key) && JSON.stringify(original[key]) !== JSON.stringify(updated[key])) {
      changes[key] = {
        from: original[key],
        to: updated[key],
      };
    }
  }

  return changes;
};

// @desc    Get all patients
// @route   GET /api/patients
// @access  Private
const getPatients = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build query
    let query = {};
    
    // Search functionality
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    // Filter by active status
    if (req.query.isActive !== undefined) {
      query.isActive = req.query.isActive === 'true';
    }

    const patients = await Patient.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(startIndex);

    const total = await Patient.countDocuments(query);

    res.status(200).json({
      success: true,
      count: patients.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
      data: patients,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single patient
// @route   GET /api/patients/:id
// @access  Private
const getPatient = async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
      });
    }

    res.status(200).json({
      success: true,
      data: patient,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new patient
// @route   POST /api/patients
// @access  Private
const createPatient = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    // Check if patient with email already exists
    const existingPatient = await Patient.findOne({ email: req.body.email });
    if (existingPatient) {
      return res.status(400).json({
        success: false,
        message: 'Patient with this email already exists',
      });
    }

    const patient = await Patient.create(req.body);

    res.status(201).json({
      success: true,
      data: patient,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update patient
// @route   PUT /api/patients/:id
// @access  Private
const updatePatient = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array(),
      });
    }

    let patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
      });
    }

    // Store original data for comparison
    const originalData = patient.toObject();

    // Check for duplicate email (excluding current patient)
    if (req.body.email && req.body.email !== patient.email) {
      const existingPatient = await Patient.findOne({
        email: req.body.email,
        _id: { $ne: req.params.id }
      });
      if (existingPatient) {
        return res.status(400).json({
          success: false,
          message: 'Patient with this email already exists',
        });
      }
    }

    // Check for duplicate phone number (excluding current patient)
    if (req.body.phone && req.body.phone !== patient.phone) {
      const existingPhone = await Patient.findOne({
        phone: req.body.phone,
        _id: { $ne: req.params.id }
      });
      if (existingPhone) {
        return res.status(400).json({
          success: false,
          message: 'Patient with this phone number already exists',
        });
      }
    }

    // Validate critical medical information changes
    const criticalFields = ['bloodGroup', 'allergies', 'currentMedications'];
    const criticalChanges = {};

    criticalFields.forEach(field => {
      if (req.body[field] && JSON.stringify(req.body[field]) !== JSON.stringify(patient[field])) {
        criticalChanges[field] = {
          from: patient[field],
          to: req.body[field],
        };
      }
    });

    // Check if patient has upcoming consultations and warn about critical changes
    if (Object.keys(criticalChanges).length > 0) {
      const upcomingConsultations = await Consultation.countDocuments({
        patient: req.params.id,
        appointmentDate: { $gte: new Date() },
        status: { $in: ['Scheduled', 'In Progress'] },
      });

      if (upcomingConsultations > 0) {
        console.log(`Critical medical information updated for patient ${patient.name} with ${upcomingConsultations} upcoming consultations`);
      }
    }

    // Update patient with validation
    patient = await Patient.findByIdAndUpdate(req.params.id, {
      ...req.body,
      updatedAt: new Date(),
    }, {
      new: true,
      runValidators: true,
    });

    // Log the update for audit trail
    console.log(`Patient updated: ${patient.name} (${patient._id}) by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Patient updated successfully',
      data: patient,
      changes: getChangedFields(originalData, patient.toObject()),
      criticalChanges: Object.keys(criticalChanges).length > 0 ? criticalChanges : undefined,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete patient (soft delete by default)
// @route   DELETE /api/patients/:id
// @access  Private (Admin only)
const deletePatient = async (req, res, next) => {
  try {
    const { force = false } = req.query; // Force delete parameter
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
      });
    }

    // Check if patient has any upcoming consultations
    const upcomingConsultations = await Consultation.find({
      patient: req.params.id,
      appointmentDate: { $gte: new Date() },
      status: { $in: ['Scheduled', 'In Progress'] },
    }).populate('doctor', 'name specialization');

    if (upcomingConsultations.length > 0 && !force) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete patient with upcoming consultations',
        upcomingConsultations: upcomingConsultations.length,
        consultations: upcomingConsultations.map(c => ({
          id: c._id,
          doctor: c.doctor.name,
          specialization: c.doctor.specialization,
          date: c.appointmentDate,
          time: c.appointmentTime,
          status: c.status,
        })),
        suggestion: 'Use ?force=true to force delete and cancel all upcoming consultations, or reschedule them first.',
      });
    }

    // Check if patient has any consultation history
    const consultationHistory = await Consultation.countDocuments({
      patient: req.params.id,
      status: 'Completed',
    });

    if (consultationHistory > 0 && !force) {
      // Soft delete - deactivate patient instead of removing
      const updatedPatient = await Patient.findByIdAndUpdate(
        req.params.id,
        {
          isActive: false,
          deactivatedAt: new Date(),
          deactivatedBy: req.user.id,
          deactivationReason: 'Deleted by admin',
        },
        { new: true }
      );

      // Cancel all upcoming consultations
      if (upcomingConsultations.length > 0) {
        await Consultation.updateMany(
          {
            patient: req.params.id,
            appointmentDate: { $gte: new Date() },
            status: { $in: ['Scheduled', 'In Progress'] },
          },
          {
            status: 'Cancelled',
            cancellationReason: 'Patient account deactivated',
            cancelledAt: new Date(),
            cancelledBy: req.user.id,
          }
        );
      }

      console.log(`Patient soft deleted: ${patient.name} (${patient._id}) by user ${req.user.id}`);

      return res.status(200).json({
        success: true,
        message: 'Patient deactivated successfully (soft delete)',
        data: {
          patient: updatedPatient,
          consultationHistory,
          cancelledUpcomingConsultations: upcomingConsultations.length,
        },
        note: 'Patient has been deactivated due to existing consultation history. Use force delete to permanently remove.',
      });
    }

    // Force delete or no consultation history
    if (force || consultationHistory === 0) {
      // Cancel all upcoming consultations if force delete
      if (upcomingConsultations.length > 0) {
        await Consultation.updateMany(
          {
            patient: req.params.id,
            appointmentDate: { $gte: new Date() },
            status: { $in: ['Scheduled', 'In Progress'] },
          },
          {
            status: 'Cancelled',
            cancellationReason: 'Patient account deleted',
            cancelledAt: new Date(),
            cancelledBy: req.user.id,
          }
        );
      }

      // Permanently delete patient
      await Patient.findByIdAndDelete(req.params.id);

      console.log(`Patient permanently deleted: ${patient.name} (${patient._id}) by user ${req.user.id}`);

      res.status(200).json({
        success: true,
        message: 'Patient deleted permanently',
        data: {
          deletedPatient: {
            id: patient._id,
            name: patient.name,
            email: patient.email,
          },
          cancelledConsultations: upcomingConsultations.length,
        },
      });
    }
  } catch (error) {
    next(error);
  }
};

// @desc    Get patient statistics
// @route   GET /api/patients/:id/stats
// @access  Private
const getPatientStats = async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
      });
    }

    // Get consultation statistics
    const totalConsultations = await Consultation.countDocuments({ patient: req.params.id });
    const completedConsultations = await Consultation.countDocuments({ 
      patient: req.params.id, 
      status: 'Completed' 
    });
    const upcomingConsultations = await Consultation.countDocuments({
      patient: req.params.id,
      appointmentDate: { $gte: new Date() },
      status: 'Scheduled',
    });

    // Get last consultation
    const lastConsultation = await Consultation.findOne({
      patient: req.params.id,
      status: 'Completed',
    }).sort({ appointmentDate: -1 }).populate('doctor', 'name specialization');

    // Calculate total amount spent
    const spentData = await Consultation.aggregate([
      { $match: { patient: patient._id, 'fee.paymentStatus': 'Paid' } },
      { $group: { _id: null, totalSpent: { $sum: '$fee.amount' } } },
    ]);

    const totalSpent = spentData.length > 0 ? spentData[0].totalSpent : 0;

    res.status(200).json({
      success: true,
      data: {
        patient: {
          id: patient._id,
          name: patient.name,
          age: patient.age,
        },
        stats: {
          totalConsultations,
          completedConsultations,
          upcomingConsultations,
          totalSpent,
          lastConsultation,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  getPatientStats,
};
