const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

let authToken = '';

// Helper function to make authenticated requests
const authenticatedRequest = (config) => {
  return axios({
    ...testConfig,
    ...config,
    headers: {
      ...testConfig.headers,
      ...(authToken && { Authorization: `Bearer ${authToken}` }),
      ...config.headers,
    },
  });
};

// Test functions
const testAuthentication = async () => {
  console.log('\n🔐 Testing Authentication...');
  
  try {
    // Test login
    const loginResponse = await axios({
      ...testConfig,
      method: 'POST',
      url: '/auth/login',
      data: {
        email: '<EMAIL>',
        password: 'admin123',
      },
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.token;
      console.log('✅ Login successful');
      console.log('👤 User:', loginResponse.data.data.name);
      console.log('📧 Email:', loginResponse.data.data.email);
      console.log('🔑 Role:', loginResponse.data.data.role);
    } else {
      console.log('❌ Login failed');
      return false;
    }

    // Test profile endpoint
    const profileResponse = await authenticatedRequest({
      method: 'GET',
      url: '/auth/me',
    });

    if (profileResponse.data.success) {
      console.log('✅ Profile fetch successful');
    } else {
      console.log('❌ Profile fetch failed');
    }

    return true;
  } catch (error) {
    console.log('❌ Authentication test failed:', error.message);
    return false;
  }
};

const testDoctors = async () => {
  console.log('\n👨‍⚕️ Testing Doctors API...');
  
  try {
    // Get all doctors
    const doctorsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/doctors',
    });

    if (doctorsResponse.data.success) {
      console.log(`✅ Retrieved ${doctorsResponse.data.count} doctors`);
      
      if (doctorsResponse.data.data.length > 0) {
        const doctor = doctorsResponse.data.data[0];
        console.log(`📋 Sample doctor: ${doctor.name} - ${doctor.specialization}`);
        
        // Test get single doctor
        const singleDoctorResponse = await authenticatedRequest({
          method: 'GET',
          url: `/doctors/${doctor._id}`,
        });
        
        if (singleDoctorResponse.data.success) {
          console.log('✅ Single doctor fetch successful');
        }

        // Test doctor stats
        const statsResponse = await authenticatedRequest({
          method: 'GET',
          url: `/doctors/${doctor._id}/stats`,
        });
        
        if (statsResponse.data.success) {
          console.log('✅ Doctor stats fetch successful');
          console.log(`📊 Total consultations: ${statsResponse.data.data.stats.totalConsultations}`);
        }
      }
    } else {
      console.log('❌ Doctors fetch failed');
    }
  } catch (error) {
    console.log('❌ Doctors test failed:', error.message);
  }
};

const testPatients = async () => {
  console.log('\n👥 Testing Patients API...');
  
  try {
    // Get all patients
    const patientsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/patients',
    });

    if (patientsResponse.data.success) {
      console.log(`✅ Retrieved ${patientsResponse.data.count} patients`);
      
      if (patientsResponse.data.data.length > 0) {
        const patient = patientsResponse.data.data[0];
        console.log(`📋 Sample patient: ${patient.name} - ${patient.email}`);
        
        // Test get single patient
        const singlePatientResponse = await authenticatedRequest({
          method: 'GET',
          url: `/patients/${patient._id}`,
        });
        
        if (singlePatientResponse.data.success) {
          console.log('✅ Single patient fetch successful');
        }
      }
    } else {
      console.log('❌ Patients fetch failed');
    }
  } catch (error) {
    console.log('❌ Patients test failed:', error.message);
  }
};

const testConsultations = async () => {
  console.log('\n📅 Testing Consultations API...');
  
  try {
    // Get all consultations
    const consultationsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/consultations',
    });

    if (consultationsResponse.data.success) {
      console.log(`✅ Retrieved ${consultationsResponse.data.count} consultations`);
      
      if (consultationsResponse.data.data.length > 0) {
        const consultation = consultationsResponse.data.data[0];
        console.log(`📋 Sample consultation: ${consultation.type} - ${consultation.status}`);
        
        // Test get single consultation
        const singleConsultationResponse = await authenticatedRequest({
          method: 'GET',
          url: `/consultations/${consultation._id}`,
        });
        
        if (singleConsultationResponse.data.success) {
          console.log('✅ Single consultation fetch successful');
        }
      }
    } else {
      console.log('❌ Consultations fetch failed');
    }
  } catch (error) {
    console.log('❌ Consultations test failed:', error.message);
  }
};

const testAnalytics = async () => {
  console.log('\n📊 Testing Analytics API...');
  
  try {
    // Test dashboard stats
    const dashboardResponse = await authenticatedRequest({
      method: 'GET',
      url: '/analytics/dashboard',
    });

    if (dashboardResponse.data.success) {
      console.log('✅ Dashboard analytics successful');
      const stats = dashboardResponse.data.data.overview;
      console.log(`📈 Total doctors: ${stats.totalDoctors}`);
      console.log(`📈 Total patients: ${stats.totalPatients}`);
      console.log(`📈 Total consultations: ${stats.totalConsultations}`);
    }

    // Test consultation analytics
    const consultationAnalyticsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/analytics/consultations',
    });

    if (consultationAnalyticsResponse.data.success) {
      console.log('✅ Consultation analytics successful');
    }

    // Test doctor performance
    const doctorPerformanceResponse = await authenticatedRequest({
      method: 'GET',
      url: '/analytics/doctors',
    });

    if (doctorPerformanceResponse.data.success) {
      console.log('✅ Doctor performance analytics successful');
    }

  } catch (error) {
    console.log('❌ Analytics test failed:', error.message);
  }
};

const testSettings = async () => {
  console.log('\n⚙️ Testing Settings API...');
  
  try {
    // Get settings
    const settingsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/settings',
    });

    if (settingsResponse.data.success) {
      console.log('✅ Settings fetch successful');
      console.log(`🏢 Platform name: ${settingsResponse.data.data.platform.platformName}`);
    } else {
      console.log('❌ Settings fetch failed');
    }
  } catch (error) {
    console.log('❌ Settings test failed:', error.message);
  }
};

const testSlots = async () => {
  console.log('\n🕐 Testing Slots API...');

  try {
    // Get doctors first
    const doctorsResponse = await authenticatedRequest({
      method: 'GET',
      url: '/doctors',
    });

    if (doctorsResponse.data.success && doctorsResponse.data.data.length > 0) {
      const doctorId = doctorsResponse.data.data[0]._id;
      const date = '2025-09-07';

      // Test available slots
      const slotsResponse = await authenticatedRequest({
        method: 'GET',
        url: `/slots/available?doctor=${doctorId}&date=${date}`,
      });

      if (slotsResponse.data.success) {
        console.log(`✅ Available slots fetch successful`);
        console.log(`🕐 Available slots: ${slotsResponse.data.data.length}`);
      }

      // Test doctor schedule
      const scheduleResponse = await authenticatedRequest({
        method: 'GET',
        url: `/slots/doctor/${doctorId}?startDate=${date}&endDate=${date}`,
      });

      if (scheduleResponse.data.success) {
        console.log('✅ Doctor schedule fetch successful');
      }
    }
  } catch (error) {
    console.log('❌ Slots test failed:', error.message);
  }
};

const testSearch = async () => {
  console.log('\n🔍 Testing Search API...');

  try {
    // Test global search
    const globalSearchResponse = await authenticatedRequest({
      method: 'GET',
      url: '/search?q=Dr&limit=5',
    });

    if (globalSearchResponse.data.success) {
      console.log('✅ Global search successful');
      console.log(`🔍 Total results: ${globalSearchResponse.data.totalResults}`);
    }

    // Test advanced doctor search
    const doctorSearchResponse = await authenticatedRequest({
      method: 'GET',
      url: '/search/doctors/advanced?specialization=General Ayurveda',
    });

    if (doctorSearchResponse.data.success) {
      console.log('✅ Advanced doctor search successful');
      console.log(`👨‍⚕️ Found ${doctorSearchResponse.data.count} doctors`);
    }

  } catch (error) {
    console.log('❌ Search test failed:', error.message);
  }
};

const testBulkOperations = async () => {
  console.log('\n📦 Testing Bulk Operations API...');

  try {
    // Test export functionality
    const exportResponse = await authenticatedRequest({
      method: 'GET',
      url: '/bulk/export/doctors',
    });

    if (exportResponse.data.success) {
      console.log('✅ Data export successful');
      console.log(`📄 Exported ${exportResponse.data.count} doctors`);
      console.log(`📁 Filename: ${exportResponse.data.filename}`);
    }

  } catch (error) {
    console.log('❌ Bulk operations test failed:', error.message);
  }
};

// Main test runner
const runAllTests = async () => {
  console.log('🚀 Starting API Tests for Ayura Admin Backend\n');
  console.log('=' .repeat(50));

  const authSuccess = await testAuthentication();
  
  if (!authSuccess) {
    console.log('\n❌ Authentication failed. Stopping tests.');
    return;
  }

  await testDoctors();
  await testPatients();
  await testConsultations();
  await testAnalytics();
  await testSettings();
  await testSlots();
  await testSearch();
  await testBulkOperations();

  console.log('\n' + '='.repeat(50));
  console.log('🎉 API Tests Completed!');
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests };
