const mongoose = require('mongoose');

const slotSchema = new mongoose.Schema({
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Doctor',
    required: [true, 'Doctor is required'],
  },
  date: {
    type: Date,
    required: [true, 'Date is required'],
  },
  startTime: {
    type: String,
    required: [true, 'Start time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
  },
  endTime: {
    type: String,
    required: [true, 'End time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: [5, 'Duration must be at least 5 minutes'],
    max: [480, 'Duration cannot exceed 8 hours'],
  },
  status: {
    type: String,
    enum: ['available', 'booked', 'blocked', 'cancelled'],
    default: 'available',
  },
  consultationType: {
    type: String,
    required: [true, 'Consultation type is required'],
    enum: [
      'General Consultation',
      'Panchakarma',
      'Pulse Diagnosis',
      'Herbal Medicine',
      'Ayurvedic Nutrition',
      'Follow-up',
      'Emergency'
    ],
  },
  fee: {
    amount: {
      type: Number,
      required: [true, 'Fee amount is required'],
      min: [0, 'Fee cannot be negative'],
    },
    currency: {
      type: String,
      default: 'INR',
      enum: ['INR', 'USD', 'EUR'],
    },
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    default: null,
  },
  consultation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Consultation',
    default: null,
  },
  isRecurring: {
    type: Boolean,
    default: false,
  },
  recurringPattern: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'weekly',
    },
    interval: {
      type: Number,
      default: 1,
      min: 1,
      max: 12,
    },
    endDate: {
      type: Date,
    },
    parentSlot: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Slot',
    },
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters'],
  },
  bookingDetails: {
    bookedAt: {
      type: Date,
    },
    bookedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    cancelledAt: {
      type: Date,
    },
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    cancellationReason: {
      type: String,
      maxlength: [200, 'Cancellation reason cannot exceed 200 characters'],
    },
  },
  metadata: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    source: {
      type: String,
      enum: ['manual', 'bulk', 'recurring', 'api'],
      default: 'manual',
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better query performance
slotSchema.index({ doctor: 1, date: 1, startTime: 1 });
slotSchema.index({ status: 1 });
slotSchema.index({ date: 1 });
slotSchema.index({ doctor: 1, status: 1 });
slotSchema.index({ patient: 1 });

// Virtual for formatted date
slotSchema.virtual('formattedDate').get(function() {
  return this.date.toISOString().split('T')[0];
});

// Virtual for slot duration in hours
slotSchema.virtual('durationHours').get(function() {
  return this.duration / 60;
});

// Virtual to check if slot is in the past
slotSchema.virtual('isPast').get(function() {
  const slotDateTime = new Date(`${this.formattedDate}T${this.startTime}`);
  return slotDateTime < new Date();
});

// Virtual to check if slot can be cancelled
slotSchema.virtual('canBeCancelled').get(function() {
  if (this.status !== 'booked') return false;
  const slotDateTime = new Date(`${this.formattedDate}T${this.startTime}`);
  const now = new Date();
  const timeDiff = slotDateTime - now;
  return timeDiff > 24 * 60 * 60 * 1000; // 24 hours in milliseconds
});

// Pre-save middleware to validate time logic
slotSchema.pre('save', function(next) {
  // Validate that end time is after start time
  const startMinutes = timeToMinutes(this.startTime);
  const endMinutes = timeToMinutes(this.endTime);
  
  if (endMinutes <= startMinutes) {
    return next(new Error('End time must be after start time'));
  }
  
  // Validate that duration matches the time difference
  const calculatedDuration = endMinutes - startMinutes;
  if (Math.abs(calculatedDuration - this.duration) > 1) {
    return next(new Error('Duration does not match start and end times'));
  }
  
  next();
});

// Pre-save middleware to prevent overlapping slots
slotSchema.pre('save', async function(next) {
  if (!this.isModified('date') && !this.isModified('startTime') && !this.isModified('endTime') && !this.isNew) {
    return next();
  }
  
  const overlappingSlot = await this.constructor.findOne({
    _id: { $ne: this._id },
    doctor: this.doctor,
    date: this.date,
    status: { $in: ['available', 'booked'] },
    $or: [
      {
        startTime: { $lt: this.endTime },
        endTime: { $gt: this.startTime }
      }
    ]
  });
  
  if (overlappingSlot) {
    return next(new Error('Slot overlaps with an existing slot'));
  }
  
  next();
});

// Static method to find available slots
slotSchema.statics.findAvailable = function(filters = {}) {
  const query = { status: 'available', ...filters };
  return this.find(query).populate('doctor', 'name specialization').sort({ date: 1, startTime: 1 });
};

// Static method to find slots by doctor and date range
slotSchema.statics.findByDoctorAndDateRange = function(doctorId, startDate, endDate) {
  return this.find({
    doctor: doctorId,
    date: { $gte: startDate, $lte: endDate }
  }).populate('patient', 'name phone').sort({ date: 1, startTime: 1 });
};

// Instance method to book the slot
slotSchema.methods.book = function(patientId, consultationId, userId) {
  if (this.status !== 'available') {
    throw new Error('Slot is not available for booking');
  }
  
  this.status = 'booked';
  this.patient = patientId;
  this.consultation = consultationId;
  this.bookingDetails.bookedAt = new Date();
  this.bookingDetails.bookedBy = userId;
  
  return this.save();
};

// Instance method to cancel the slot
slotSchema.methods.cancel = function(reason, userId) {
  if (this.status !== 'booked') {
    throw new Error('Only booked slots can be cancelled');
  }
  
  this.status = 'available';
  this.patient = null;
  this.consultation = null;
  this.bookingDetails.cancelledAt = new Date();
  this.bookingDetails.cancelledBy = userId;
  this.bookingDetails.cancellationReason = reason;
  
  return this.save();
};

// Helper function to convert time string to minutes
function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

module.exports = mongoose.model('Slot', slotSchema);
