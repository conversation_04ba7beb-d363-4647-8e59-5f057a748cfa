const express = require('express');
const {
  getDashboardStats,
  getRevenueAnalytics,
  getConsultationAnalytics,
  getDoctorPerformance,
  getPatientAnalytics,
} = require('../controllers/analyticsController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Routes
router.get('/dashboard', getDashboardStats);
router.get('/revenue', authorize('admin'), getRevenueAnalytics);
router.get('/consultations', getConsultationAnalytics);
router.get('/doctors', getDoctorPerformance);
router.get('/patients', getPatientAnalytics);

module.exports = router;
